// src/services/RAGService.ts
import { validateEnv } from '../config/env.config';
import { pool } from '../database/client';
import { VoyageAIClient } from 'voyageai';
import pgvector from 'pgvector/pg';
import { logger } from '../config/logger.config';
import { RAGVerse } from '../types'; // Import the new type

const env = validateEnv();
const vo = new VoyageAIClient({ apiKey: env.VOYAGE_AI_API_KEY }); // Use VoyageAIClient instead

export class RAGService {
  /**
   * Finds the most semantically similar Bible verses for a given query.
   * @param queryText The user's question or statement.
   * @param limit The number of verses to retrieve.
   * @returns An array of verse objects.
   */
  public static async search(queryText: string, limit = 5) {
    try {
      logger.info({ query: queryText }, 'Generating embedding for user query');

      // 1. Generate an embedding for the user's query with retry logic
      const result = await this.generateEmbeddingWithRetry(queryText);

      if (!result) {
        logger.warn({ query: queryText }, 'Failed to generate embedding after retries, returning empty results');
        return [];
      }

      const queryEmbedding = result.data![0].embedding;

      // 2. Perform a vector similarity search in the database
      const vectorSql = pgvector.toSql(queryEmbedding);
      const dbQuery = `
        SELECT book, chapter, verse, text, (embedding <-> $1) AS distance
        FROM rag_content
        ORDER BY embedding <-> $1
        LIMIT $2;
      `;
      const { rows } = await pool.query(dbQuery, [vectorSql, limit]);

      logger.info({ query: queryText, count: rows.length }, 'Found relevant verses from RAG search.');
      return rows;

    } catch (error) {
      logger.error({ error }, 'Error in RAGService search');
      return []; // Return an empty array on error
    }
  }

  /**
   * Generate embedding with retry logic for rate limiting
   */
  private static async generateEmbeddingWithRetry(queryText: string, maxRetries = 3): Promise<any | null> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await vo.embed({
          input: [queryText],
          model: 'voyage-3-large',
        });
        return result;
      } catch (error: any) {
        // Check if it's a rate limiting error (429)
        if (error.statusCode === 429) {
          const waitTime = Math.pow(2, attempt) * 1000; // Exponential backoff: 2s, 4s, 8s
          logger.warn({
            attempt,
            maxRetries,
            waitTime,
            query: queryText
          }, 'Rate limited by Voyage AI, retrying after delay');

          if (attempt < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, waitTime));
            continue;
          } else {
            logger.error({
              error,
              query: queryText
            }, 'Rate limit exceeded after all retries');
            return null;
          }
        } else {
          // For non-rate-limit errors, throw immediately
          throw error;
        }
      }
    }
    return null;
  }
}