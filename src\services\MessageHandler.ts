// src/services/MessageHandler.ts
import { createHash } from 'crypto';
import { logger } from '../config/logger.config';
import { StateManager } from './StateManager';
import { OnboardingHandler } from './OnboardingHandler';
import { SessionService } from './SessionService';
import { SessionHandler } from './SessionHandler';
import { SessionHandlerV3 } from './SessionHandlerV3';
import { SessionModeService } from './SessionModeService';
import { RAGService } from './RAGService';
import { AIService } from './AIService';
import { messageQueue } from '../queue/messageQueue';
import { MemoryService } from './MemoryService';
import { User } from '../types';
import { ensureMinTypingDelay, sendPaced } from '../utils/messagePacer';
import { shouldUseSessionModeLockIn } from '../config/features.config';

async function handleGeneralMessage(user: User, message: string, userJid: string, payload: any, wasDelayedCheckIn = false) {
  const command = message.toLowerCase();
  if (command === 'start study') {
    logger.info({ userId: user.id }, 'User initiated a new Bible study session.');

    // Use new Session Mode Lock-in system if enabled
    if (shouldUseSessionModeLockIn()) {
      return SessionModeService.handleSessionStart(user, userJid);
    } else {
      // Use original system
      const newSession = await SessionService.startBibleStudySession(user.id, user.current_book || 'John');
      return SessionHandler.handle(newSession, user, message);
    }
  }

  // Handle "show confessions" command to retrieve stored personal confessions
  if (command === 'show confessions') {
    logger.info({ userId: user.id, query: 'show confessions' }, 'Handling show confessions command');

    try {
      const confession = await StateManager.getUserConfession(user.id);

      if (!confession) {
        await sendPaced(userJid, "You don't have any saved confessions yet. Type 'add confession' to create one.", { split: false });
        return;
      }

      const confessionMessage = `Here's your saved confession:\n\n"${confession.refined_text}"\n\nCreated on ${confession.created_at.toLocaleDateString()}.\n\nYou can type 'edit confession' to update it or 'add confession' to create a new one.`;
      await sendPaced(userJid, confessionMessage, { split: false });
      return;

    } catch (error) {
      logger.error({ error, userId: user.id }, 'Error retrieving user confession');
      await sendPaced(userJid, "I'm sorry, there was an issue retrieving your confession. Please try again.", { split: false });
      return;
    }
  }

  logger.info({ userId: user.id, query: message }, 'Handling general Q&A with memory');
  await MemoryService.addMessageToHistory(user.id, { role: 'user', content: message });
  // Queue a 'typing…' presence and capture the time it was sent
  const typingStartedAt = Date.now();
  await messageQueue.add('send-presence', { toJid: userJid, presence: 'composing' });
  try {
    const conversationHistory = await MemoryService.getHistory(user.id);
    const contextVerses = await RAGService.search(message);
    if (contextVerses.length > 0 && contextVerses[0].distance > 0.35) {
      contextVerses.length = 0;
    }

    // If this was a delayed response to a check-in, add an acknowledgment prefix
    let answer;
    if (wasDelayedCheckIn) {
      // Get response as normal
      const baseAnswer = await AIService.generateAnswer(message, contextVerses, conversationHistory, user);
      // Add acknowledgment prefix
      const userName = user.name ? `, ${user.name}` : '';
      answer = `Hey${userName}, I noticed you didn't get back to me when I checked in earlier — I hope you're doing okay.\n\n${baseAnswer}`;
    } else {
      answer = await AIService.generateAnswer(message, contextVerses, conversationHistory, user);
    }

    // Calculate a natural delay based on message length (consistent with other handlers)
    const typingDelay = Math.min(2500, 800 + (answer?.length || 0) * 12);
    await ensureMinTypingDelay(typingStartedAt, typingDelay);

    await sendPaced(userJid, answer, { delayMs: 1800, split: true });
    await MemoryService.addMessageToHistory(user.id, { role: 'assistant', content: answer });
  } catch (error) {
    logger.error({ error, userId: user.id }, "Error in Q&A flow");
    await sendPaced(userJid, "I'm having some trouble thinking right now.", { split: false });
  } finally {
    messageQueue.add('send-presence', { toJid: userJid, presence: 'paused' });
  }
}

export async function handleIncomingMessage(payload: any): Promise<void> {
  const messageText = payload.data?.message?.conversation?.trim() || '';
  const userJid = payload.data?.key?.remoteJid;
  if (payload.event !== 'messages.upsert' || payload.data?.key?.fromMe || !userJid) {
    return;
  }
  const userHash = createHash('sha256').update(userJid).digest('hex');
  const user = await StateManager.findOrCreateUser(userHash, userJid);

  /*
   * Delegate all onboarding and access-code verification logic to OnboardingHandler.
   * This ensures that the very first message from a brand-new user only acts as a
   * trigger – it will never be interpreted as their access code. The dedicated
   * OnboardingHandler manages the welcome message, access-code prompt, and every
   * subsequent onboarding step.
   */

  if (user.onboarding_step !== 'ONBOARDING_COMPLETE') {
    return OnboardingHandler.handle(user, messageText, userJid);
  }

  // ---------------- TEST TRIGGER FOR CHECK-IN (JDZE) ----------------
  if (messageText.trim().toUpperCase() === 'JDZE') {
    const active = await SessionService.findActiveSession(user.id);
    if (!active) {
      const session = await SessionService.startSession(user.id, { type: 'CHECK_IN', expiresInMinutes: 60 });
      await SessionHandler.handle(session, user, '');
    }
    return; // do not process further
  }
  // ------------------------------------------------------------------

  const activeSession = await SessionService.findActiveSession(user.id);

  // Manual exit from Bible study - handle various stop commands
  const stopCommands = ['stop study', 'end study', 'quit study', 'exit study', 'stop session', 'end session'];
  const userCommand = messageText.trim().toLowerCase();

  if (activeSession?.session_type === 'BIBLE_STUDY' && stopCommands.some(cmd => userCommand === cmd || userCommand.includes(cmd))) {
    await SessionService.endSession(activeSession.id, 'COMPLETED');
    await sendPaced(userJid, "Got it – we'll pause the Bible study for now. Type 'start study' whenever you're ready to continue your journey!", { split: false });
    // Early return – do not route empty message to Q&A which caused confusion previously
    return;
  }

  // Also handle stop commands when no active session but user wants to stop any ongoing interaction
  if (!activeSession && stopCommands.some(cmd => userCommand === cmd || userCommand.includes(cmd))) {
    await sendPaced(userJid, "There's no active study session to stop right now. Type 'start study' to begin a new Bible study session!", { split: false });
    return;
  }

  // Handle confession commands (case-insensitive)
  if (!activeSession) {
    if (userCommand.includes('add confession') || userCommand === 'confession') {
      // Start a new confession session
      const session = await SessionService.startSession(user.id, {
        type: 'BIBLE_STUDY',
        step: 'CONFESSION_PENDING_INPUT',
        context: { isOnboarding: false }
      });

      await sendPaced(userJid, "Okay, let's begin. What would you like to confess or reflect on today? You can type it out, and I'll help you refine it.", { split: false });
      return;
    }

    if (userCommand.includes('edit confession')) {
      // Check if user has an existing confession
      const existingConfession = await StateManager.getUserConfession(user.id);

      if (!existingConfession) {
        await sendPaced(userJid, "You don't have a confession to edit yet. Type 'add confession' to create one first.", { split: false });
        return;
      }

      // Start an edit confession session
      const session = await SessionService.startSession(user.id, {
        type: 'BIBLE_STUDY',
        step: 'CONFESSION_EDIT_PENDING_INPUT',
        context: {
          isOnboarding: false,
          isEditing: true,
          editingConfessionId: existingConfession.id
        }
      });

      await sendPaced(userJid, `Okay, ${user.name}, please type out the updated version of your confession. I'll help refine it once you're done.`, { split: false });
      return;
    }
  }

  if (activeSession) {
    // Use new Session Mode Lock-in handler if enabled, otherwise use original
    if (shouldUseSessionModeLockIn()) {
      return SessionHandlerV3.handle(activeSession, user, messageText);
    } else {
      return SessionHandler.handle(activeSession, user, messageText);
    }
  }
  
  // Check if there was a recent expired check-in session
  const wasDelayedCheckIn = await checkForExpiredCheckIn(user.id);
  
  await handleGeneralMessage(user, messageText, userJid, payload, wasDelayedCheckIn);
}

/**
 * Checks if there was a recent (within the last 24h) check-in session that expired
 * This helps identify if we need to acknowledge a late response
 */
async function checkForExpiredCheckIn(userId: string): Promise<boolean> {
  // Prevent repeating the acknowledgment within 24h by caching in Redis
  const { redisConnection } = await import('../queue/connection');
  const cacheKey = `expired_checkin_ack:${userId}`;

  // If we've already acknowledged recently, bail early
  const alreadyAcked = await redisConnection.get(cacheKey);
  if (alreadyAcked) return false;

  const query = `
    SELECT id FROM study_sessions 
    WHERE user_id = $1 
      AND session_type = 'CHECK_IN' 
      AND status = 'EXPIRED'
      AND updated_at > NOW() - INTERVAL '24 hours'
    ORDER BY updated_at DESC
    LIMIT 1;
  `;

  try {
    const { rows } = await (await import('../database/client')).pool.query(query, [userId]);
    if (rows.length > 0) {
      // Mark as acknowledged for the next 24 hours
      await redisConnection.setex(cacheKey, 24 * 60 * 60, '1');
      return true;
    }
    return false;
  } catch (error) {
    logger.error({ error, userId }, 'Error checking for expired check-in sessions');
    return false;
  }
}