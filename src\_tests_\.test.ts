// src/_tests_/health.test.ts
import request from 'supertest';
import express from 'express';

// We create a minimal app instance for testing this endpoint
const app = express();
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

describe('GET /health', () => {
  it('should return 200 OK and a status message', async () => {
    const response = await request(app).get('/health');
    expect(response.status).toBe(200);
    expect(response.body).toEqual({ status: 'ok' });
  });
});