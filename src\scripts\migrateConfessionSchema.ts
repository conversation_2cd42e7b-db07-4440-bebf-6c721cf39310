// src/scripts/migrateConfessionSchema.ts
// Migration script for confession feature database changes

import { config } from 'dotenv';
import { Pool } from 'pg';
import pino from 'pino';

// Load environment variables
config();

const logger = pino({ transport: { target: 'pino-pretty' } });

async function migrateConfessionSchema() {
  const dbUrl = process.env.DATABASE_URL;
  if (!dbUrl) {
    logger.error('❌ DATABASE_URL environment variable not set.');
    process.exit(1);
  }

  const pool = new Pool({ connectionString: dbUrl });
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    logger.info('🚀 Starting confession feature schema migration...');
    
    // 1. Add new onboarding step enum value
    logger.info('Adding AWAITING_CONFESSION_CHOICE to onboarding_step_status enum...');
    await client.query(`
      DO $$
      BEGIN
        ALTER TYPE onboarding_step_status ADD VALUE IF NOT EXISTS 'AWAITING_CONFESSION_CHOICE';
      EXCEPTION WHEN duplicate_object THEN 
        RAISE NOTICE 'AWAITING_CONFESSION_CHOICE enum value already exists';
      END $$;
    `);
    
    // 2. Add new study step enum values for confession flow
    logger.info('Adding confession-related study_step enum values...');
    await client.query(`
      DO $$
      BEGIN
        ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CONFESSION_PENDING_INPUT';
        ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CONFESSION_AWAITING_CONFIRMATION';
        ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CONFESSION_AWAITING_REMINDER_TIME';
        ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CONFESSION_EDIT_PENDING_INPUT';
        ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CONFESSION_EDIT_AWAITING_CONFIRMATION';
      EXCEPTION WHEN duplicate_object THEN 
        RAISE NOTICE 'Confession study_step enum values already exist';
      END $$;
    `);
    
    // 3. Create confessions table
    logger.info('Creating confessions table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS confessions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        original_text TEXT NOT NULL,
        refined_text TEXT NOT NULL,
        reminder_time TIME WITHOUT TIME ZONE,
        reminder_frequency TEXT DEFAULT 'once',
        next_reminder_at TIMESTAMPTZ,
        is_active BOOLEAN DEFAULT true NOT NULL,
        created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
      );
    `);
    
    // 4. Create indexes for confessions table
    logger.info('Creating confession indexes...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_confessions_user_id
        ON confessions (user_id)
        WHERE is_active = true;
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_confessions_reminder_due
        ON confessions (next_reminder_at)
        WHERE is_active = true AND next_reminder_at IS NOT NULL;
    `);
    
    // 5. Create trigger for updating confession updated_at timestamp
    logger.info('Creating confession updated_at trigger...');
    await client.query(`
      CREATE OR REPLACE FUNCTION touch_confession_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    await client.query(`
      DROP TRIGGER IF EXISTS trg_touch_confession ON confessions;
      CREATE TRIGGER trg_touch_confession
        BEFORE UPDATE ON confessions
        FOR EACH ROW EXECUTE FUNCTION touch_confession_updated_at();
    `);
    
    // 6. Verify the migration
    logger.info('Verifying confession schema migration...');
    
    // Check if confessions table exists and has correct structure
    const tableCheck = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'confessions'
      ORDER BY ordinal_position;
    `);
    
    if (tableCheck.rows.length === 0) {
      throw new Error('Confessions table was not created successfully');
    }
    
    logger.info({
      columns: tableCheck.rows.map(row => ({
        name: row.column_name,
        type: row.data_type,
        nullable: row.is_nullable
      }))
    }, 'Confessions table structure verified');
    
    // Check if indexes exist
    const indexCheck = await client.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE tablename = 'confessions';
    `);
    
    logger.info({
      indexes: indexCheck.rows.map(row => row.indexname)
    }, 'Confession indexes verified');
    
    await client.query('COMMIT');
    logger.info('✅ Confession feature schema migration completed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error({ error }, '❌ Confession schema migration failed');
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  migrateConfessionSchema()
    .then(() => {
      logger.info('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error({ error }, 'Migration failed');
      process.exit(1);
    });
}

export { migrateConfessionSchema };
