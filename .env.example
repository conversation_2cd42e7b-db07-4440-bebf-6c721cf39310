# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/paro_bible_bot

# Redis Configuration
REDIS_URL=redis://localhost:6379

# API Keys
GROQ_API_KEY=your_groq_api_key_here
VOYAGE_AI_API_KEY=your_voyage_ai_api_key_here

# Access Code for Bot
ACCESS_CODE=PARO2025

# Server Configuration
PORT=3000
NODE_ENV=development

# Feature Flags - Session Mode Lock-in System (NEW)
# Set to 'false' to disable, anything else (or omitted) enables the feature

# Main session mode system - ENABLED by default
ENABLE_SESSION_MODE_LOCK_IN=true

# Individual study modes - all ENABLED by default
ENABLE_DEEP_DIVE_MODE=true
ENABLE_CHAPTER_OVERVIEW_MODE=true
ENABLE_EXPLORE_THEME_MODE=true

# Memory and welcome features - ENABLED by default
ENABLE_INTELLIGENT_WELCOME=true
ENABLE_ENHANCED_MEMORY=true

# Dynamic chunking for chapter overview - ENABLED by default
ENABLE_DYNAMIC_CHUNKING=true

# Advanced system prompts - ENABLED by default
ENABLE_ADVANCED_PROMPTS=true

# Legacy Settings (for backward compatibility)
VERSE_BATCH_SIZE=3
