{"name": "paro-bible-bot-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "db:seed": "ts-node src/scripts/seedDatabase.ts", "db:embed": "ts-node src/scripts/generateEmbeddings.ts", "db:migrate": "ts-node src/scripts/migrateSchema.ts", "db:migrate:prod": "node dist/scripts/migrateSchema.js", "db:seed:prod": "node dist/scripts/seedDatabase.js", "db:embed:prod": "node dist/scripts/generateEmbeddings.js", "db:fix-sessions": "ts-node src/scripts/fixStudySessions.ts", "db:fix-sessions:prod": "node dist/scripts/fixStudySessions.js", "db:migrate-session-mode": "ts-node src/scripts/migrateSessionModeSchema.ts", "db:migrate-session-mode:prod": "node dist/scripts/migrateSessionModeSchema.js", "test:session-mode": "ts-node src/scripts/testSessionModeIntegration.ts", "test:session-mode:prod": "node dist/scripts/testSessionModeIntegration.js", "validate:performance": "ts-node src/scripts/validateSessionModePerformance.ts", "validate:performance:prod": "node dist/scripts/validateSessionModePerformance.js", "db:migrate-confession": "ts-node src/scripts/migrateConfessionSchema.ts", "db:migrate-confession:prod": "node dist/scripts/migrateConfessionSchema.js", "test:confession": "ts-node src/scripts/testConfessionFeature.ts", "test:confession:prod": "node dist/scripts/testConfessionFeature.js", "validate:confession": "ts-node src/scripts/validateConfessionTypes.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "axios": "^1.10.0", "bullmq": "^5.56.1", "chrono-node": "^2.8.3", "crypto-js": "^4.2.0", "dotenv": "^17.0.0", "express": "^5.1.0", "groq-sdk": "^0.26.0", "ioredis": "^5.6.1", "luxon": "^3.6.1", "node-cron": "^4.2.0", "pg": "^8.16.3", "pgvector": "^0.2.1", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "voyageai": "^0.0.4", "zod": "^3.25.67"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/luxon": "^3.6.2", "@types/node": "^24.0.8", "@types/node-cron": "^3.0.11", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "eslint": "^9.30.0", "ioredis-mock": "^8.9.0", "jest": "^30.0.3", "prettier": "^3.6.2", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}