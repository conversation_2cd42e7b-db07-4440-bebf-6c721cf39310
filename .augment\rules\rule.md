---
type: "manual"
---

Requirements for implementing a fix:
Non-destructive approach: Ensure all fixes preserve existing functionality and don't break current working features

Systems thinking: Consider the broader impact of code changes on the entire application architecture, including state management, component interactions, and user experience flows

Root Cause Identification
Focus on identifying and addressing the underlying cause of a problem, not just its surface symptoms.
Avoid patchwork solutions that only suppress visible issues without resolving their origin.
Use debugging tools, logs, and historical context to trace the issue to its source before applying any fix.

Consequence analysis: Evaluate potential side effects, performance implications, and edge cases before implementing changes

High-level architectural perspective: Think holistically about how these fixes integrate with the overall system design, data flow, and component hierarchy

Post-Fix Summary Requirement
At the conclusion of every fix, include a concise summary describing:

What was changed and why

How the change addresses the issue

In what ways the implementation adheres to the four requirements above