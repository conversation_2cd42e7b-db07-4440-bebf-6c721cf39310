duvtant@main-app-host:~/projects/paro-bible-bot$ docker compose logs -f paro-backend
paro-backend-1  |
paro-backend-1  | > paro-bible-bot-backend@1.0.0 db:migrate:prod
paro-backend-1  | > node dist/scripts/migrateSchema.js
paro-backend-1  |
paro-backend-1  | [dotenv@17.0.0] injecting env (0) from .env – 🔐 encrypt with dotenvx: https://dotenvx.com
paro-backend-1  | [13:57:31.496] INFO (19): 🚀 Applying database schema migrations...
paro-backend-1  | [13:57:31.551] INFO (19): ✅ Schema migration applied successfully!
paro-backend-1  | npm notice
paro-backend-1  | npm notice New major version of npm available! 10.8.2 -> 11.5.1
paro-backend-1  | npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.5.1
paro-backend-1  | npm notice To update run: npm install -g npm@11.5.1
paro-backend-1  | npm notice
paro-backend-1  |
paro-backend-1  | > paro-bible-bot-backend@1.0.0 start
paro-backend-1  | > node dist/index.js
paro-backend-1  |
paro-backend-1  | Raw DATABASE_URL: *********************************************************/paro_bible_bot
paro-backend-1  | Raw REDIS_URL: redis://redis:6379
paro-backend-1  | !!! DEBUG: Raw EVOLUTION_API_URL is: https://paro-bot-1-evo-api.6g0gbc.easypanel.host
paro-backend-1  | Raw DATABASE_URL: *********************************************************/paro_bible_bot
paro-backend-1  | Raw REDIS_URL: redis://redis:6379
paro-backend-1  | !!! DEBUG: Raw EVOLUTION_API_URL is: https://paro-bot-1-evo-api.6g0gbc.easypanel.host
paro-backend-1  | Raw DATABASE_URL: *********************************************************/paro_bible_bot
paro-backend-1  | Raw REDIS_URL: redis://redis:6379
paro-backend-1  | !!! DEBUG: Raw EVOLUTION_API_URL is: https://paro-bot-1-evo-api.6g0gbc.easypanel.host
paro-backend-1  | Raw DATABASE_URL: *********************************************************/paro_bible_bot
paro-backend-1  | Raw REDIS_URL: redis://redis:6379
paro-backend-1  | !!! DEBUG: Raw EVOLUTION_API_URL is: https://paro-bot-1-evo-api.6g0gbc.easypanel.host
paro-backend-1  | Raw DATABASE_URL: *********************************************************/paro_bible_bot
paro-backend-1  | Raw REDIS_URL: redis://redis:6379
paro-backend-1  | !!! DEBUG: Raw EVOLUTION_API_URL is: https://paro-bot-1-evo-api.6g0gbc.easypanel.host
paro-backend-1  | Raw DATABASE_URL: *********************************************************/paro_bible_bot
paro-backend-1  | Raw REDIS_URL: redis://redis:6379
paro-backend-1  | !!! DEBUG: Raw EVOLUTION_API_URL is: https://paro-bot-1-evo-api.6g0gbc.easypanel.host
paro-backend-1  | Raw DATABASE_URL: *********************************************************/paro_bible_bot
paro-backend-1  | Raw REDIS_URL: redis://redis:6379
paro-backend-1  | !!! DEBUG: Raw EVOLUTION_API_URL is: https://paro-bot-1-evo-api.6g0gbc.easypanel.host
paro-backend-1  | {"level":30,"time":1753538253120,"pid":41,"hostname":"f7cb05ad99ab","msg":"Paro Bible Bot Backend is starting..."}
paro-backend-1  | ✅ PostgreSQL database connected successfully!
paro-backend-1  | {"level":30,"time":1753538253242,"pid":41,"hostname":"f7cb05ad99ab","msg":"Server is listening on port 3000"}
paro-backend-1  | {"level":30,"time":1753538253242,"pid":41,"hostname":"f7cb05ad99ab","msg":"Message worker is running and connected to Redis: true"}
paro-backend-1  | {"level":30,"time":1753538253242,"pid":41,"hostname":"f7cb05ad99ab","msg":"⏰ SchedulerService started."}
paro-backend-1  | {"level":30,"time":1753538299927,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"1","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538299927,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538300185,"pid":41,"hostname":"f7cb05ad99ab","jobId":"1","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538301800,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"AWAITING_AUTH","msg":"Updated onboarding_step"}
paro-backend-1  | {"level":30,"time":1753538301937,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"2","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538301937,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538301938,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Welcome to Paro Bible Bot! To get started, please enter the access code to verify access.\""}
paro-backend-1  | {"level":30,"time":1753538302526,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB0B6B0A8A2790DAD53F64BD625E6AE354CFC1D"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538302531,"pid":41,"hostname":"f7cb05ad99ab","jobId":"2","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538303937,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"3","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538303938,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538303965,"pid":41,"hostname":"f7cb05ad99ab","jobId":"3","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538307357,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","msg":"User marked as verified"}
paro-backend-1  | {"level":30,"time":1753538307361,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"4","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538307361,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538307392,"pid":41,"hostname":"f7cb05ad99ab","jobId":"4","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538309607,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"5","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538309607,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538309609,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Thank you! You're now verified. I'm Paro, your digital companion for a meaningful Bible journey. What's your first name?\""}
paro-backend-1  | {"level":30,"time":1753538309621,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"AWAITING_NAME","msg":"Updated onboarding_step"}
paro-backend-1  | {"level":30,"time":1753538309965,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB02EB037543581E2CD695D850CAF42ADBE0CB8"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538309974,"pid":41,"hostname":"f7cb05ad99ab","jobId":"5","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538311614,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"6","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538311614,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538311648,"pid":41,"hostname":"f7cb05ad99ab","jobId":"6","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538316865,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","msg":"Updated user name"}
paro-backend-1  | {"level":30,"time":1753538316872,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"7","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538316873,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538317001,"pid":41,"hostname":"f7cb05ad99ab","jobId":"7","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538319379,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"8","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538319379,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538319379,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"It's truly wonderful to connect with you, David! I'd love to help you find a consistent rhythm with God's Word.\n\nTo set up your daily reminder, what time usually works best for you? (e.g., \"8am\" or \"7:30 PM\")\""}
paro-backend-1  | {"level":30,"time":1753538319393,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"AWAITING_REMINDER_TIME","msg":"Updated onboarding_step"}
paro-backend-1  | {"level":30,"time":1753538319725,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB002E206A487021FF199E89489F93F1A95824A"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538319728,"pid":41,"hostname":"f7cb05ad99ab","jobId":"8","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538321386,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"9","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538321386,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538321407,"pid":41,"hostname":"f7cb05ad99ab","jobId":"9","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538329905,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","time":"15:02","msg":"Updated user reminder time"}
paro-backend-1  | {"level":30,"time":1753538329910,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"10","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538329910,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538330054,"pid":41,"hostname":"f7cb05ad99ab","jobId":"10","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538332417,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"11","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538332417,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538332417,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Perfect, David! Your daily reminder is set for around 15:02. To make sure it reaches you at the right moment, could you share which country you're in? (e.g., Nigeria, UK, USA)\""}
paro-backend-1  | {"level":30,"time":1753538332426,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"AWAITING_TIMEZONE","msg":"Updated onboarding_step"}
paro-backend-1  | {"level":30,"time":1753538332781,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB09857784753021179C5B56F78BBB12A0B96B3"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538332784,"pid":41,"hostname":"f7cb05ad99ab","jobId":"11","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538334419,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"12","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538334419,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538334440,"pid":41,"hostname":"f7cb05ad99ab","jobId":"12","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538335593,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","timezone":"Africa/Lagos","msg":"Updated user timezone"}
paro-backend-1  | {"level":30,"time":1753538335605,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","nextReminderAt":"2025-07-26T14:02:00.000Z","msg":"Scheduled first reminder"}
paro-backend-1  | {"level":30,"time":1753538335613,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","nextTime":"2025-07-27T11:39:00.000Z","msg":"Scheduled initial check-in"}
paro-backend-1  | {"level":30,"time":1753538335613,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","msg":"Scheduled first check-in"}
paro-backend-1  | {"level":30,"time":1753538336422,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"13","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538336422,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538336440,"pid":41,"hostname":"f7cb05ad99ab","jobId":"13","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538338125,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"AWAITING_JOURNEY","msg":"Updated onboarding_step"}
paro-backend-1  | {"level":30,"time":1753538338424,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"14","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538338424,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538338424,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Now, let's choose where to begin our journey into God's Word together!\n\nWhich book of the Bible would you like to start with? (For example: John, Genesis, Psalms, Romans, etc.)\""}
paro-backend-1  | {"level":30,"time":1753538338769,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB07192B749B1BD958257762C5ABB242084999E"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538338777,"pid":41,"hostname":"f7cb05ad99ab","jobId":"14","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538340432,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"15","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538340432,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538340458,"pid":41,"hostname":"f7cb05ad99ab","jobId":"15","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538342101,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","book":"Romans","msg":"Updated user journey"}
paro-backend-1  | {"level":30,"time":1753538342433,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"16","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538342434,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538342452,"pid":41,"hostname":"f7cb05ad99ab","jobId":"16","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538344610,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"17","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538344610,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538344610,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Excellent choice, David! You're all set for a wonderful journey in Romans.\n\nBefore we begin, I'd love to offer you something special. Would you like to add a personal confession or reflection? I can help you refine it and set up a gentle reminder for you later. This is completely optional.\n\nType \"yes\" if you'd like to add a confession, or \"no\" to complete your setup and start studying.\""}
paro-backend-1  | {"level":30,"time":1753538344619,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"AWAITING_CONFESSION_CHOICE","msg":"Updated onboarding_step"}
paro-backend-1  | {"level":30,"time":1753538344957,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB01964FE06192337196640D45DA63DDA775C1B"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538344960,"pid":41,"hostname":"f7cb05ad99ab","jobId":"17","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538346616,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"18","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538346616,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538346644,"pid":41,"hostname":"f7cb05ad99ab","jobId":"18","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538350079,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"19","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538350079,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538350095,"pid":41,"hostname":"f7cb05ad99ab","jobId":"19","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538352326,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"20","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538352326,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538352326,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Okay, let's begin. What would you like to confess or reflect on today? You can type it out, and I'll help you refine it.\""}
paro-backend-1  | {"level":30,"time":1753538352336,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"AWAITING_CONFESSION_INPUT","msg":"Updated onboarding_step"}
paro-backend-1  | {"level":30,"time":1753538352349,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","sessionId":"2162af6a-131a-4f03-aa46-a28083a9e09d","type":"BIBLE_STUDY","msg":"Started new session"}
paro-backend-1  | {"level":30,"time":1753538352676,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB045C20A165989A09D0200C0058FBDCD9D7373"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538352679,"pid":41,"hostname":"f7cb05ad99ab","jobId":"20","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538354331,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"21","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538354331,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538354362,"pid":41,"hostname":"f7cb05ad99ab","jobId":"21","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538366325,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"2162af6a-131a-4f03-aa46-a28083a9e09d","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_PENDING_INPUT","studyMode":"DEEP_DIVE","msg":"Handling Bible study message with Session Mode Lock-in"}
paro-backend-1  | {"level":30,"time":1753538366327,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"2162af6a-131a-4f03-aa46-a28083a9e09d","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_PENDING_INPUT","isOnboarding":true,"msg":"Handling confession session step"}
paro-backend-1  | {"level":30,"time":1753538366333,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"22","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538366333,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538366470,"pid":41,"hostname":"f7cb05ad99ab","jobId":"22","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538368130,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"2162af6a-131a-4f03-aa46-a28083a9e09d","msg":"Updated session context"}
paro-backend-1  | {"level":30,"time":1753538368336,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"23","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538368336,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538368336,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Thank you for sharing. Let me help you refine this...\""}
paro-backend-1  | {"level":30,"time":1753538368687,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB03122DF853CA4C7325105C2353A3015376237"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538368694,"pid":41,"hostname":"f7cb05ad99ab","jobId":"23","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538370341,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"24","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538370341,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538370360,"pid":41,"hostname":"f7cb05ad99ab","jobId":"24","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538370642,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"2162af6a-131a-4f03-aa46-a28083a9e09d","newStep":"CONFESSION_AWAITING_CONFIRMATION","msg":"Updated session step"}
paro-backend-1  | {"level":30,"time":1753538372343,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"25","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538372343,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538372363,"pid":41,"hostname":"f7cb05ad99ab","jobId":"25","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538374347,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"26","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538374347,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538374347,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Thank you for sharing, David. I've refined it slightly to help you articulate it clearly. Does this capture what you wanted to express?\n\n\"I am deeply favored. Though some turn against me, I continue to shine.\"\n\nType \"yes\" if this looks good, or \"no\" if you'd like to re-enter your confession.\""}
paro-backend-1  | {"level":30,"time":1753538374710,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB0DFB4023F8C7AEB4CFBE00A76B36928A8CE32"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538374715,"pid":41,"hostname":"f7cb05ad99ab","jobId":"26","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538376350,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"27","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538376350,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538376369,"pid":41,"hostname":"f7cb05ad99ab","jobId":"27","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538381451,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"2162af6a-131a-4f03-aa46-a28083a9e09d","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_AWAITING_CONFIRMATION","studyMode":"DEEP_DIVE","msg":"Handling Bible study message with Session Mode Lock-in"}
paro-backend-1  | {"level":30,"time":1753538381453,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"2162af6a-131a-4f03-aa46-a28083a9e09d","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_AWAITING_CONFIRMATION","isOnboarding":true,"msg":"Handling confession session step"}
paro-backend-1  | {"level":30,"time":1753538381456,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"28","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538381456,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538381605,"pid":41,"hostname":"f7cb05ad99ab","jobId":"28","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538383960,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"29","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538383961,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538383961,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Okay, David. Now, what time would you like me to gently remind you of this confession? (e.g., 'every morning at 7am', 'every Sunday at 10pm', 'just once tomorrow at noon').\""}
paro-backend-1  | {"level":30,"time":1753538383972,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"2162af6a-131a-4f03-aa46-a28083a9e09d","newStep":"CONFESSION_AWAITING_REMINDER_TIME","msg":"Updated session step"}
paro-backend-1  | {"level":30,"time":1753538384299,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB0F708AFECA934E01AB798B8E9B7D5D168E290"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538384302,"pid":41,"hostname":"f7cb05ad99ab","jobId":"29","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538385965,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"30","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538385965,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538385988,"pid":41,"hostname":"f7cb05ad99ab","jobId":"30","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538394613,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"2162af6a-131a-4f03-aa46-a28083a9e09d","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_AWAITING_REMINDER_TIME","studyMode":"DEEP_DIVE","msg":"Handling Bible study message with Session Mode Lock-in"}
paro-backend-1  | {"level":30,"time":1753538394613,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"2162af6a-131a-4f03-aa46-a28083a9e09d","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_AWAITING_REMINDER_TIME","isOnboarding":true,"msg":"Handling confession session step"}
paro-backend-1  | {"level":30,"time":1753538395109,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","confessionId":"2368ab29-921d-4856-ac11-c6d15ac9f0b8","msg":"Saved new confession"}
paro-backend-1  | {"level":30,"time":1753538395115,"pid":41,"hostname":"f7cb05ad99ab","confessionId":"2368ab29-921d-4856-ac11-c6d15ac9f0b8","nextReminderAt":"2025-07-26T15:05:00.000Z","msg":"Scheduled confession reminder"}
paro-backend-1  | {"level":30,"time":1753538395120,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"31","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538395120,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538395255,"pid":41,"hostname":"f7cb05ad99ab","jobId":"31","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538397389,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"32","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538397389,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538397389,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Got it, David! I'll gently remind you about your confession at 4:05 PM. You can also ask me to 'show confessions' anytime.\""}
paro-backend-1  | {"level":30,"time":1753538397738,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB01C7261D7FCE16BE34313BF785C629C129950"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538397742,"pid":41,"hostname":"f7cb05ad99ab","jobId":"32","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538399394,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"33","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538399395,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538399420,"pid":41,"hostname":"f7cb05ad99ab","jobId":"33","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538399903,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"ONBOARDING_COMPLETE","msg":"Updated onboarding_step"}
paro-backend-1  | {"level":30,"time":1753538399908,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"2162af6a-131a-4f03-aa46-a28083a9e09d","finalStatus":"COMPLETED","msg":"Session ended"}
paro-backend-1  | {"level":30,"time":1753538401397,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"34","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538401397,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538401417,"pid":41,"hostname":"f7cb05ad99ab","jobId":"34","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538403398,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"35","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538403398,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538403398,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Your confession has been saved and you're all set up! Feel free to simply type 'start study' whenever you're ready to dive in, or ask me any questions about God's Word. I'm excited for what we'll discover together!\""}
paro-backend-1  | {"level":30,"time":1753538403726,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB0384B58136290521809C34F13DBA99C54DEB0"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538403728,"pid":41,"hostname":"f7cb05ad99ab","jobId":"35","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538405402,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"36","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538405402,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538405417,"pid":41,"hostname":"f7cb05ad99ab","jobId":"36","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538411877,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","query":"okay","msg":"Handling general Q&A with memory"}
paro-backend-1  | {"level":30,"time":1753538411881,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","msg":"Added message to conversation history."}
paro-backend-1  | {"level":30,"time":1753538411888,"pid":41,"hostname":"f7cb05ad99ab","query":"okay","msg":"Generating embedding for user query"}
paro-backend-1  | {"level":30,"time":1753538411938,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"37","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538411939,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538412094,"pid":41,"hostname":"f7cb05ad99ab","jobId":"37","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538413101,"pid":41,"hostname":"f7cb05ad99ab","query":"okay","count":5,"msg":"Found relevant verses from RAG search."}
paro-backend-1  | {"level":30,"time":1753538413103,"pid":41,"hostname":"f7cb05ad99ab","query":"okay","count":0,"msg":"Using verses for answer generation."}
paro-backend-1  | {"level":30,"time":1753538413104,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending prompt to Groq for answer generation."}
paro-backend-1  | {"level":30,"time":1753538414389,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"38","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538414389,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538414389,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Hey David! Glad you're journeying through Romans-it's such a rich letter. Paul really hits his stride in Romans 8, where he writes, \"The Spirit himself testifies with our spirit that we are God's children\" (Romans 8:16). I love how that verse lands right after all the heavy theology of chapters 1-7. Since you're in Romans, is there a particular section or theme that's been jumping out at you lately?\""}
paro-backend-1  | {"level":30,"time":1753538414397,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","msg":"Added message to conversation history."}
paro-backend-1  | {"level":30,"time":1753538414773,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB0DB1BCB1380624AFD1B2ED999A2CF7880B32E"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538414775,"pid":41,"hostname":"f7cb05ad99ab","jobId":"38","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538416395,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"39","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538416396,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538416418,"pid":41,"hostname":"f7cb05ad99ab","jobId":"39","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538436059,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","query":"show confessions","msg":"Handling show confessions command"}
paro-backend-1  | {"level":30,"time":1753538436066,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"40","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538436066,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538436066,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Here's your saved confession:\n\n\"I am deeply favored. Though some turn against me, I continue to shine.\"\n\nCreated on 7/26/2025.\n\nYou can type 'edit confession' to update it or 'add confession' to create a new one.\""}
paro-backend-1  | {"level":30,"time":1753538436530,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB073D65CE6292D2154E1AD0D394EF0A5357905"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538436533,"pid":41,"hostname":"f7cb05ad99ab","jobId":"40","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538445956,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","sessionId":"b00aa539-ba9a-4655-aa41-276c300e6c41","type":"BIBLE_STUDY","msg":"Started new session"}
paro-backend-1  | {"level":30,"time":1753538445962,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"41","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538445963,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538445963,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Okay, let's begin. What would you like to confess or reflect on today? You can type it out, and I'll help you refine it.\""}
paro-backend-1  | {"level":30,"time":1753538446447,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB0EB4364A22D7FFF1D38A6AD263F7324FE08B3"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538446453,"pid":41,"hostname":"f7cb05ad99ab","jobId":"41","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538457404,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"42","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538457404,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538457489,"pid":41,"hostname":"f7cb05ad99ab","jobId":"42","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538459280,"pid":41,"hostname":"f7cb05ad99ab","userId":"deb292fa-44b7-417c-9479-8c8da3eecefe","step":"AWAITING_AUTH","msg":"Updated onboarding_step"}
paro-backend-1  | {"level":30,"time":1753538459407,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"43","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538459407,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538459408,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Welcome to Paro Bible Bot! To get started, please enter the access code to verify access.\""}
paro-backend-1  | {"level":30,"time":1753538461410,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"44","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538461410,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538461549,"pid":41,"hostname":"f7cb05ad99ab","jobId":"44","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538467373,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"b00aa539-ba9a-4655-aa41-276c300e6c41","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_PENDING_INPUT","studyMode":"DEEP_DIVE","msg":"Handling Bible study message with Session Mode Lock-in"}
paro-backend-1  | {"level":30,"time":1753538467373,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"b00aa539-ba9a-4655-aa41-276c300e6c41","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_PENDING_INPUT","isOnboarding":false,"msg":"Handling confession session step"}
paro-backend-1  | {"level":30,"time":1753538467375,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"45","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538467375,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538467501,"pid":41,"hostname":"f7cb05ad99ab","jobId":"45","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538469163,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"b00aa539-ba9a-4655-aa41-276c300e6c41","msg":"Updated session context"}
paro-backend-1  | {"level":30,"time":1753538469381,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"46","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538469381,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538469381,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Thank you for sharing. Let me help you refine this...\""}
paro-backend-1  | {"level":30,"time":1753538469735,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB01F1477857E61DC007E29134DD60D59D95E3D"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538469739,"pid":41,"hostname":"f7cb05ad99ab","jobId":"46","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538471387,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"47","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538471387,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538471405,"pid":41,"hostname":"f7cb05ad99ab","jobId":"47","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538471677,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"b00aa539-ba9a-4655-aa41-276c300e6c41","newStep":"CONFESSION_AWAITING_CONFIRMATION","msg":"Updated session step"}
paro-backend-1  | {"level":30,"time":1753538473395,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"48","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538473395,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538473411,"pid":41,"hostname":"f7cb05ad99ab","jobId":"48","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538475395,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"49","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538475395,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538475395,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Thank you for sharing, David. I've refined it slightly to help you articulate it clearly. Does this capture what you wanted to express?\n\n\"I am blessed and highly favored, planted by God Almighty.\"\n\nType \"yes\" if this looks good, or \"no\" if you'd like to re-enter your confession.\""}
paro-backend-1  | {"level":30,"time":1753538475737,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB0C6829B9B159EB39EB4C3EB9CF29CA6F916F1"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538475740,"pid":41,"hostname":"f7cb05ad99ab","jobId":"49","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538477397,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"50","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538477397,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538477420,"pid":41,"hostname":"f7cb05ad99ab","jobId":"50","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538479658,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"b00aa539-ba9a-4655-aa41-276c300e6c41","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_AWAITING_CONFIRMATION","studyMode":"DEEP_DIVE","msg":"Handling Bible study message with Session Mode Lock-in"}
paro-backend-1  | {"level":30,"time":1753538479658,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"b00aa539-ba9a-4655-aa41-276c300e6c41","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_AWAITING_CONFIRMATION","isOnboarding":false,"msg":"Handling confession session step"}
paro-backend-1  | {"level":30,"time":1753538479662,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"51","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538479662,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538479676,"pid":41,"hostname":"f7cb05ad99ab","jobId":"51","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538482168,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"52","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538482168,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538482168,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Okay, David. Now, what time would you like me to gently remind you of this confession? (e.g., 'every morning at 7am', 'every Sunday at 10pm', 'just once tomorrow at noon').\""}
paro-backend-1  | {"level":30,"time":1753538482176,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"b00aa539-ba9a-4655-aa41-276c300e6c41","newStep":"CONFESSION_AWAITING_REMINDER_TIME","msg":"Updated session step"}
paro-backend-1  | {"level":30,"time":1753538482509,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB051E567EC1519F25F8587273240848B1EBDDB"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538482512,"pid":41,"hostname":"f7cb05ad99ab","jobId":"52","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538484172,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"53","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538484173,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538484196,"pid":41,"hostname":"f7cb05ad99ab","jobId":"53","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538495431,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"b00aa539-ba9a-4655-aa41-276c300e6c41","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_AWAITING_REMINDER_TIME","studyMode":"DEEP_DIVE","msg":"Handling Bible study message with Session Mode Lock-in"}
paro-backend-1  | {"level":30,"time":1753538495432,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"b00aa539-ba9a-4655-aa41-276c300e6c41","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","step":"CONFESSION_AWAITING_REMINDER_TIME","isOnboarding":false,"msg":"Handling confession session step"}
paro-backend-1  | {"level":30,"time":1753538495438,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","confessionId":"cb2d4574-f175-4606-bb6d-3dd21c9571de","msg":"Saved new confession"}
paro-backend-1  | {"level":30,"time":1753538495445,"pid":41,"hostname":"f7cb05ad99ab","confessionId":"cb2d4574-f175-4606-bb6d-3dd21c9571de","nextReminderAt":"2025-07-26T15:05:00.000Z","msg":"Scheduled confession reminder"}
paro-backend-1  | {"level":30,"time":1753538495448,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"54","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538495448,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538495576,"pid":41,"hostname":"f7cb05ad99ab","jobId":"54","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538497714,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"55","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538497714,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538497714,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Got it, David! I'll gently remind you about your confession at 4:05 PM. You can also ask me to 'show confessions' anytime.\""}
paro-backend-1  | {"level":30,"time":1753538498039,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB09CD25C4727FA763CFCCBE0A0E1E11D9EF2F3"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538498040,"pid":41,"hostname":"f7cb05ad99ab","jobId":"55","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538499721,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"56","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538499721,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538499743,"pid":41,"hostname":"f7cb05ad99ab","jobId":"56","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538499933,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"b00aa539-ba9a-4655-aa41-276c300e6c41","finalStatus":"COMPLETED","msg":"Session ended"}
paro-backend-1  | {"level":30,"time":1753538501720,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"57","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538501721,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538501740,"pid":41,"hostname":"f7cb05ad99ab","jobId":"57","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538503722,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"58","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538503722,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538503722,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Your confession has been saved. Feel free to continue with your Bible study or ask me any questions about God's Word.\""}
paro-backend-1  | {"level":30,"time":1753538504055,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB015D8BF217E8467ABE3CD7BA19E0737D0AE90"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538504057,"pid":41,"hostname":"f7cb05ad99ab","jobId":"58","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538505733,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"59","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538505733,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538505750,"pid":41,"hostname":"f7cb05ad99ab","jobId":"59","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":50,"time":1753538520595,"pid":41,"hostname":"f7cb05ad99ab","status":400,"data":{"status":400,"error":"Bad Request","response":{"message":["Error: Timed Out"]}},"msg":"Evolution API validation error"}
paro-backend-1  | {"level":50,"time":1753538520604,"pid":41,"hostname":"f7cb05ad99ab","jobId":"43","jobName":"send-text","error":{"message":"Request failed with status code 400","name":"AxiosError","stack":"AxiosError: Request failed with status code 400\n    at settle (/app/node_modules/axios/dist/node/axios.cjs:2053:12)\n    at IncomingMessage.handleStreamEnd (/app/node_modules/axios/dist/node/axios.cjs:3170:11)\n    at IncomingMessage.emit (node:events:536:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/app/node_modules/axios/dist/node/axios.cjs:4280:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async EvolutionClient.sendTextMessage (/app/dist/services/EvolutionClient.js:30:30)\n    at async Worker.processMessageJob [as processFn] (/app/dist/queue/messageWorker.js:49:13)\n    at async /app/node_modules/bullmq/dist/cjs/classes/worker.js:493:32\n    at async Worker.retryIfFailed (/app/node_modules/bullmq/dist/cjs/classes/worker.js:769:24)","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json","apikey":"601ED7064693-4E5B-9B20-0A5D57941115","User-Agent":"axios/1.10.0","Content-Length":"135","Accept-Encoding":"gzip, compress, deflate, br"},"baseURL":"https://paro-bot-1-evo-api.6g0gbc.easypanel.host","method":"post","url":"/message/sendText/parot","data":"{\"number\":\"<EMAIL>\",\"text\":\"Welcome to Paro Bible Bot! To get started, please enter the access code to verify access.\"}","allowAbsoluteUrls":true},"code":"ERR_BAD_REQUEST","status":400},"msg":"Job failed after all retries"}
paro-backend-1  | {"level":30,"time":1753538525704,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"43","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538525704,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538525704,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Welcome to Paro Bible Bot! To get started, please enter the access code to verify access.\""}
paro-backend-1  | {"level":50,"time":1753538586546,"pid":41,"hostname":"f7cb05ad99ab","status":400,"data":{"status":400,"error":"Bad Request","response":{"message":["Error: Timed Out"]}},"msg":"Evolution API validation error"}
paro-backend-1  | {"level":50,"time":1753538586553,"pid":41,"hostname":"f7cb05ad99ab","jobId":"43","jobName":"send-text","error":{"message":"Request failed with status code 400","name":"AxiosError","stack":"AxiosError: Request failed with status code 400\n    at settle (/app/node_modules/axios/dist/node/axios.cjs:2053:12)\n    at IncomingMessage.handleStreamEnd (/app/node_modules/axios/dist/node/axios.cjs:3170:11)\n    at IncomingMessage.emit (node:events:536:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/app/node_modules/axios/dist/node/axios.cjs:4280:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async EvolutionClient.sendTextMessage (/app/dist/services/EvolutionClient.js:30:30)\n    at async Worker.processMessageJob [as processFn] (/app/dist/queue/messageWorker.js:49:13)\n    at async /app/node_modules/bullmq/dist/cjs/classes/worker.js:493:32\n    at async Worker.retryIfFailed (/app/node_modules/bullmq/dist/cjs/classes/worker.js:769:24)","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json","apikey":"601ED7064693-4E5B-9B20-0A5D57941115","User-Agent":"axios/1.10.0","Content-Length":"135","Accept-Encoding":"gzip, compress, deflate, br"},"baseURL":"https://paro-bot-1-evo-api.6g0gbc.easypanel.host","method":"post","url":"/message/sendText/parot","data":"{\"number\":\"<EMAIL>\",\"text\":\"Welcome to Paro Bible Bot! To get started, please enter the access code to verify access.\"}","allowAbsoluteUrls":true},"code":"ERR_BAD_REQUEST","status":400},"msg":"Job failed after all retries"}
paro-backend-1  | {"level":30,"time":1753538596649,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"43","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538596649,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538596649,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Welcome to Paro Bible Bot! To get started, please enter the access code to verify access.\""}
paro-backend-1  | {"level":50,"time":1753538657624,"pid":41,"hostname":"f7cb05ad99ab","status":400,"data":{"status":400,"error":"Bad Request","response":{"message":["Error: Timed Out"]}},"msg":"Evolution API validation error"}
paro-backend-1  | {"level":50,"time":1753538657628,"pid":41,"hostname":"f7cb05ad99ab","jobId":"43","jobName":"send-text","error":{"message":"Request failed with status code 400","name":"AxiosError","stack":"AxiosError: Request failed with status code 400\n    at settle (/app/node_modules/axios/dist/node/axios.cjs:2053:12)\n    at IncomingMessage.handleStreamEnd (/app/node_modules/axios/dist/node/axios.cjs:3170:11)\n    at IncomingMessage.emit (node:events:536:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/app/node_modules/axios/dist/node/axios.cjs:4280:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async EvolutionClient.sendTextMessage (/app/dist/services/EvolutionClient.js:30:30)\n    at async Worker.processMessageJob [as processFn] (/app/dist/queue/messageWorker.js:49:13)\n    at async /app/node_modules/bullmq/dist/cjs/classes/worker.js:493:32\n    at async Worker.retryIfFailed (/app/node_modules/bullmq/dist/cjs/classes/worker.js:769:24)","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json","apikey":"601ED7064693-4E5B-9B20-0A5D57941115","User-Agent":"axios/1.10.0","Content-Length":"135","Accept-Encoding":"gzip, compress, deflate, br"},"baseURL":"https://paro-bot-1-evo-api.6g0gbc.easypanel.host","method":"post","url":"/message/sendText/parot","data":"{\"number\":\"<EMAIL>\",\"text\":\"Welcome to Paro Bible Bot! To get started, please enter the access code to verify access.\"}","allowAbsoluteUrls":true},"code":"ERR_BAD_REQUEST","status":400},"msg":"Job failed after all retries"}
paro-backend-1  | {"level":30,"time":1753538700086,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","sessionId":"46ef0e8f-5d00-4032-ae35-f142604b713c","type":"REMINDER","msg":"Started new session"}
paro-backend-1  | {"level":30,"time":1753538700095,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"60","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538700095,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"composing","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538700211,"pid":41,"hostname":"f7cb05ad99ab","jobId":"60","jobName":"send-presence","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538701362,"pid":41,"hostname":"f7cb05ad99ab","sessionId":"46ef0e8f-5d00-4032-ae35-f142604b713c","finalStatus":"COMPLETED","msg":"Session ended"}
paro-backend-1  | {"level":30,"time":1753538701365,"pid":41,"hostname":"f7cb05ad99ab","userId":"f7e52773-f815-4f93-a0b6-9b202b214b12","msg":"Sent Bible study reminder and scheduled next one"}
paro-backend-1  | {"level":30,"time":1753538702098,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-text","jobId":"61","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538702098,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","msg":"Sending text message"}
paro-backend-1  | {"level":30,"time":1753538702098,"pid":41,"hostname":"f7cb05ad99ab","msg":"Sending <NAME_EMAIL>: \"Hi David! It's time for your daily Bible study. Type \"start study\" to begin.\""}
paro-backend-1  | {"level":30,"time":1753538702453,"pid":41,"hostname":"f7cb05ad99ab","key":{"remoteJid":"<EMAIL>","fromMe":true,"id":"3EB01BC6D2737182FF17F2007036B7A111079261"},"msg":"✅ Message sent successfully"}
paro-backend-1  | {"level":30,"time":1753538702455,"pid":41,"hostname":"f7cb05ad99ab","jobId":"61","jobName":"send-text","msg":"Job completed successfully"}
paro-backend-1  | {"level":30,"time":1753538704098,"pid":41,"hostname":"f7cb05ad99ab","jobName":"send-presence","jobId":"62","msg":"Processing job"}
paro-backend-1  | {"level":30,"time":1753538704098,"pid":41,"hostname":"f7cb05ad99ab","toJid":"<EMAIL>","presence":"paused","msg":"Sending presence state"}
paro-backend-1  | {"level":30,"time":1753538704110,"pid":41,"hostname":"f7cb05ad99ab","jobId":"62","jobName":"send-presence","msg":"Job completed successfully"}