duvtant@main-app-host:~/metabase$ docker compose logs -f metabase
WARN[0000] /home/<USER>/metabase/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion
metabase-1  | Warning: environ value jdk-11.0.23+9 for key :java-version has been overwritten with 11.0.23
metabase-1  | 2025-07-27 17:11:37,757 INFO metabase.util :: Maximum memory available to JVM: 954.0 MB
metabase-1  | 2025-07-27 17:11:40,905 INFO util.encryption :: Saved credentials encryption is DISABLED for this Metabase instance. 🔓
metabase-1  |  For more information, see https://metabase.com/docs/latest/operations-guide/encrypting-database-details-at-rest.html
metabase-1  | 2025-07-27 17:11:50,612 INFO driver.impl :: Registered abstract driver :sql  🚚
metabase-1  | 2025-07-27 17:11:50,630 INFO driver.impl :: Registered abstract driver :sql-jdbc (parents: [:sql]) 🚚
metabase-1  | 2025-07-27 17:11:50,641 INFO metabase.util :: Load driver :sql-jdbc took 119.8 ms
metabase-1  | 2025-07-27 17:11:50,642 INFO driver.impl :: Registered driver :h2 (parents: [:sql-jdbc]) 🚚
metabase-1  | 2025-07-27 17:11:50,949 INFO driver.impl :: Registered driver :mysql (parents: [:sql-jdbc]) 🚚
metabase-1  | 2025-07-27 17:11:50,998 INFO driver.impl :: Registered driver :postgres (parents: [:sql-jdbc]) 🚚
metabase-1  | 2025-07-27 17:11:54,219 INFO metabase.core ::
metabase-1  | Metabase v0.49.8 (38cb850)
metabase-1  |
metabase-1  | Copyright © 2025 Metabase, Inc.
metabase-1  |
metabase-1  | Metabase Enterprise Edition extensions are NOT PRESENT.
metabase-1  | 2025-07-27 17:11:54,250 INFO metabase.core :: Starting Metabase in STANDALONE mode
metabase-1  | 2025-07-27 17:11:54,356 INFO metabase.server :: Launching Embedded Jetty Webserver with config:
metabase-1  |  {:port 3000, :host "0.0.0.0"}
metabase-1  |
metabase-1  | 2025-07-27 17:11:54,478 INFO metabase.core :: Starting Metabase version v0.49.8 (38cb850) ...
metabase-1  | 2025-07-27 17:11:54,487 INFO metabase.core :: System info:
metabase-1  |  {"file.encoding" "UTF-8",
metabase-1  |  "java.runtime.name" "OpenJDK Runtime Environment",
metabase-1  |  "java.runtime.version" "11.0.23+9",
metabase-1  |  "java.vendor" "Eclipse Adoptium",
metabase-1  |  "java.vendor.url" "https://adoptium.net/",
metabase-1  |  "java.version" "11.0.23",
metabase-1  |  "java.vm.name" "OpenJDK 64-Bit Server VM",
metabase-1  |  "java.vm.version" "11.0.23+9",
metabase-1  |  "os.name" "Linux",
metabase-1  |  "os.version" "5.15.0-142-generic",
metabase-1  |  "user.language" "en",
metabase-1  |  "user.timezone" "GMT"}
metabase-1  |
metabase-1  | 2025-07-27 17:11:54,494 INFO metabase.plugins :: Loading plugins in /plugins...
metabase-1  | 2025-07-27 17:11:54,762 INFO util.files :: Extract file /modules/athena.metabase-driver.jar -> /plugins/athena.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:54,937 INFO util.files :: Extract file /modules/presto-jdbc.metabase-driver.jar -> /plugins/presto-jdbc.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:55,057 INFO util.files :: Extract file /modules/snowflake.metabase-driver.jar -> /plugins/snowflake.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:55,863 INFO util.files :: Extract file /modules/oracle.metabase-driver.jar -> /plugins/oracle.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:55,868 INFO util.files :: Extract file /modules/googleanalytics.metabase-driver.jar -> /plugins/googleanalytics.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:55,899 INFO util.files :: Extract file /modules/vertica.metabase-driver.jar -> /plugins/vertica.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:55,905 INFO util.files :: Extract file /modules/mongo.metabase-driver.jar -> /plugins/mongo.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:55,954 INFO util.files :: Extract file /modules/redshift.metabase-driver.jar -> /plugins/redshift.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:55,970 INFO util.files :: Extract file /modules/sqlite.metabase-driver.jar -> /plugins/sqlite.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:56,165 INFO util.files :: Extract file /modules/sqlserver.metabase-driver.jar -> /plugins/sqlserver.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:56,184 INFO util.files :: Extract file /modules/sparksql.metabase-driver.jar -> /plugins/sparksql.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:56,341 INFO util.files :: Extract file /modules/druid.metabase-driver.jar -> /plugins/druid.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:56,366 INFO util.files :: Extract file /modules/bigquery-cloud-sdk.metabase-driver.jar -> /plugins/bigquery-cloud-sdk.metabase-driver.jar
metabase-1  | 2025-07-27 17:11:57,351 INFO plugins.dependencies :: Metabase cannot initialize plugin Metabase Vertica Driver due to required dependencies. Metabase requires the Vertica JDBC driver in order to connect to Vertica databases, but we can't ship it as part of Metabase due to licensing restrictions. See https://metabase.com/docs/latest/administration-guide/databases/vertica.html for more details.
metabase-1  |
metabase-1  | 2025-07-27 17:11:57,354 INFO plugins.dependencies :: Metabase Vertica Driver dependency {:class com.vertica.jdbc.Driver} satisfied? false
metabase-1  | 2025-07-27 17:11:57,356 INFO plugins.dependencies :: Plugins with unsatisfied deps: ["Metabase Vertica Driver"]
metabase-1  | 2025-07-27 17:11:57,402 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :presto-jdbc...
metabase-1  | 2025-07-27 17:11:57,404 INFO driver.impl :: Registered driver :presto-jdbc (parents: [:sql-jdbc]) 🚚
metabase-1  | 2025-07-27 17:11:57,419 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :druid...
metabase-1  | 2025-07-27 17:11:57,433 INFO driver.impl :: Registered driver :druid  🚚
metabase-1  | 2025-07-27 17:11:57,439 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :googleanalytics...
metabase-1  | 2025-07-27 17:11:57,455 INFO driver.impl :: Registered driver :googleanalytics  🚚
metabase-1  | 2025-07-27 17:11:57,484 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :athena...
metabase-1  | 2025-07-27 17:11:57,485 INFO driver.impl :: Registered driver :athena (parents: [:sql-jdbc]) 🚚
metabase-1  | 2025-07-27 17:11:57,510 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :mongo...
metabase-1  | 2025-07-27 17:11:57,511 INFO driver.impl :: Registered driver :mongo  🚚
metabase-1  | 2025-07-27 17:11:57,530 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :bigquery-cloud-sdk...
metabase-1  | 2025-07-27 17:11:57,532 INFO driver.impl :: Registered driver :bigquery-cloud-sdk (parents: [:sql]) 🚚
metabase-1  | 2025-07-27 17:11:57,539 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :redshift...
metabase-1  | 2025-07-27 17:11:57,542 INFO driver.impl :: Registered driver :redshift (parents: [:postgres]) 🚚
metabase-1  | 2025-07-27 17:11:57,554 INFO plugins.dependencies :: Metabase cannot initialize plugin Metabase Oracle Driver due to required dependencies. Metabase requires the Oracle JDBC driver in order to connect to Oracle databases, but we can't ship it as part of Metabase due to licensing restrictions. See https://metabase.com/docs/latest/administration-guide/databases/oracle.html for more details.
metabase-1  |
metabase-1  | 2025-07-27 17:11:57,556 INFO plugins.dependencies :: Metabase Oracle Driver dependency {:class oracle.jdbc.OracleDriver} satisfied? false
metabase-1  | 2025-07-27 17:11:57,559 INFO plugins.dependencies :: Plugins with unsatisfied deps: ["Metabase Oracle Driver" "Metabase Vertica Driver"]
metabase-1  | 2025-07-27 17:11:57,568 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :sqlserver...
metabase-1  | 2025-07-27 17:11:57,570 INFO driver.impl :: Registered driver :sqlserver (parents: [:sql-jdbc]) 🚚
metabase-1  | 2025-07-27 17:11:57,586 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :hive-like...
metabase-1  | 2025-07-27 17:11:57,587 INFO driver.impl :: Registered abstract driver :hive-like (parents: [:sql-jdbc]) 🚚
metabase-1  | 2025-07-27 17:11:57,588 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :sparksql...
metabase-1  | 2025-07-27 17:11:57,589 INFO driver.impl :: Registered driver :sparksql (parents: [:hive-like]) 🚚
metabase-1  | 2025-07-27 17:11:57,637 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :snowflake...
metabase-1  | 2025-07-27 17:11:57,638 INFO driver.impl :: Registered driver :snowflake (parents: [:sql-jdbc]) 🚚
metabase-1  | 2025-07-27 17:11:57,642 DEBUG plugins.lazy-loaded-driver :: Registering lazy loading driver :sqlite...
metabase-1  | 2025-07-27 17:11:57,643 INFO driver.impl :: Registered driver :sqlite (parents: [:sql-jdbc]) 🚚
metabase-1  | 2025-07-27 17:11:57,650 INFO metabase.core :: Setting up and migrating Metabase DB. Please sit tight, this may take a minute...
metabase-1  | 2025-07-27 17:11:57,653 INFO db.setup :: Verifying postgres Database Connection ...
metabase-1  | 2025-07-27 17:11:57,712 ERROR middleware.log :: GET /api/health 503 2.8 ms (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:00,091 ERROR middleware.log :: GET /api/health 503 643.6 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:00,916 ERROR middleware.log :: GET /api/health 503 327.8 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:01,832 ERROR middleware.log :: GET /api/health 503 448.0 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:02,630 ERROR middleware.log :: GET /api/health 503 846.3 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:03,522 ERROR middleware.log :: GET /api/health 503 350.8 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:04,660 ERROR middleware.log :: GET /api/health 503 748.6 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:05,634 ERROR middleware.log :: GET /api/health 503 835.8 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:06,465 ERROR middleware.log :: GET /api/health 503 901.5 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:07,384 ERROR middleware.log :: GET /api/health 503 578.1 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:08,208 ERROR middleware.log :: GET /api/health 503 393.1 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:09,127 ERROR middleware.log :: GET /api/health 503 438.5 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:09,914 ERROR middleware.log :: GET /api/health 503 556.9 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:11,095 ERROR middleware.log :: GET /api/health 503 677.5 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:11,837 ERROR middleware.log :: GET /api/health 503 1.6 ms (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:12,718 ERROR middleware.log :: GET /api/health 503 714.0 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:13,420 ERROR middleware.log :: GET /api/health 503 745.8 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:14,153 ERROR middleware.log :: GET /api/health 503 898.9 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:15,077 ERROR middleware.log :: GET /api/health 503 637.2 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:15,790 ERROR middleware.log :: GET /api/health 503 647.7 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:16,550 ERROR middleware.log :: GET /api/health 503 667.6 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:17,322 ERROR middleware.log :: GET /api/health 503 1.1 ms (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:18,069 ERROR middleware.log :: GET /api/health 503 631.1 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:18,884 ERROR middleware.log :: GET /api/health 503 585.0 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:19,713 ERROR middleware.log :: GET /api/health 503 646.4 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:21,629 ERROR middleware.log :: GET /api/health 503 682.8 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:23,202 ERROR middleware.log :: GET /api/health 503 674.0 µs (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:24,170 ERROR middleware.log :: GET /api/health 503 1.5 ms (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}
metabase-1  |
metabase-1  | 2025-07-27 17:12:25,066 ERROR middleware.log :: GET /api/health 503 1.2 ms (0 DB calls)
metabase-1  | {:status "initializing", :progress 0.3}