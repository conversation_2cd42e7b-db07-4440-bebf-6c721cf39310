// src/services/AIService.ts
import { Groq } from 'groq-sdk';
import { logger } from '../config/logger.config';
import { ChatMessage, RAGVerse, User } from '../types';
import { validateEnv } from '../config/env.config';
import { PromptService } from './PromptService';
import { filterAIResponse, filterBibleStudyResponse } from '../utils/textFilter';

// Get validated environment variables
const env = validateEnv();

const groq = new Groq({
  apiKey: env.GROQ_API_KEY,
});

export class AIService {
  /**
   * Generate an answer to a user's question using RAG context and conversation history
   */
  public static async generateAnswer(
    query: string,
    contextVerses: RAGVerse[],
    conversationHistory: ChatMessage[] = [],
    user?: User
  ): Promise<string> {
    logger.info({ query, count: contextVerses.length }, 'Using verses for answer generation.');

    const verseContext = contextVerses.map(v => `${v.book} ${v.chapter}:${v.verse} - ${v.text}`).join('\n\n');

    // Include user context in the prompt
    const userName = user?.name || 'friend';
    const currentBook = user?.current_book || undefined;

    const systemPrompt = PromptService.getContextualPrompt({
      mode: 'GENERAL',
      userName,
      book: currentBook
    });

    logger.info('Sending prompt to Groq for answer generation.');

    try {
      const userContext = user ? `
        User context:
        - Name: ${userName}
        ${currentBook ? `- Currently studying: ${currentBook}` : ''}
        ${user.current_streak ? `- Study streak: ${user.current_streak} days` : ''}
      ` : '';

      const messages = [
        { role: 'system' as const, content: systemPrompt },
        ...conversationHistory.map(msg => ({
          role: msg.role as 'user' | 'assistant' | 'system',
          content: msg.content
        })),
        {
          role: 'user' as const,
          content: `
            ${userContext}

            My question is: ${query}

            ${contextVerses.length > 0 ? `Here are some Bible verses that might be relevant:\n\n${verseContext}` : ''}

            Please respond in a friendly, conversational way, and cite specific verses when relevant.
          `
        }
      ];

      const completion = await groq.chat.completions.create({
        messages,
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 800,
      });

      const response = completion.choices[0].message.content || "I'm not sure how to answer that right now.";
      return filterAIResponse(response);
    } catch (error) {
      logger.error({ error }, 'Error generating answer with Groq');
      return "I'm sorry, I'm having trouble thinking right now. Could you try again in a moment?";
    }
  }

  /**
   * Generate a scene-setting introduction for a Bible book study
   */
  public static async generateSceneSetter(book: string): Promise<string> {
    const prompt = `
      Create a brief, engaging introduction to studying the book of ${book} in the Bible.
      Keep it warm, conversational, and under 100 words.
      Focus on why this book is meaningful and what the reader might discover.
      End with a simple question like "Ready to dive in?" to create engagement.
    `;

    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 200,
      });

      return completion.choices[0].message.content || `Let's explore the book of ${book} together. This is going to be a wonderful journey of discovery. Ready to begin?`;
    } catch (error) {
      logger.error({ error, book }, 'Error generating scene setter');
      return `Let's explore the book of ${book} together. This is going to be a wonderful journey of discovery. Ready to begin?`;
    }
  }

  /**
   * Generate a reflection question for Bible study verses
   */
  public static async generateReflectionQuestion(verses: any[]): Promise<string> {
    if (!verses || verses.length === 0) {
      return "What stands out to you in this passage?";
    }

    const verseText = verses.map(v => `[${v.chapter}:${v.verse}] ${v.text}`).join('\n\n');
    const reference = `${verses[0].book} ${verses[0].chapter}:${verses[0].verse}-${verses[verses.length - 1].verse}`;

    const prompt = `
      You are Paro, a Bible study companion using the Guided Discovery method.
      
      These verses were just shared with the user:
      ${verseText}
      
      Create ONE open-ended, personal reflection question about ${reference} that:
      - Invites the user to share what resonates with THEM emotionally or spiritually
      - Focuses on impressions, feelings, or personal connections rather than analysis
      - Uses phrases like "What stands out to you?" or "How does this make you feel?"
      - Is warm, gentle, and conversational in tone
      - Is concise (1-2 sentences)
      
      Format your response as a single question without any additional text.
    `;

    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 150,
      });

      const response = completion.choices[0].message.content || `What stands out to you in this passage from ${reference}?`;
      return filterAIResponse(response);
    } catch (error) {
      logger.error({ error }, 'Error generating reflection question');
      return `What stands out to you in this passage from ${reference}?`;
    }
  }

  /**
   * Generate a personalized response to a user's Bible study insight
   */
  public static async generateBibleStudyResponse(options: {
    userMessage: string;
    currentPassage: string;
    questionType: 'observation' | 'interpretation' | 'application';
    sessionHistory: Array<{passage: string, response: string, type: string, timestamp: string}>;
    nextQuestionType: 'interpretation' | 'application' | 'next_passage';
  }): Promise<{response: string, followUpQuestion: string}> {
    const { userMessage, currentPassage, questionType, sessionHistory, nextQuestionType } = options;
    
    // Format session history for context
    const historyContext = sessionHistory.length > 0 
      ? `Previous insights from this session:\n${sessionHistory.map(h => 
          `- On ${h.passage}: "${h.response}" (${h.type})`).join('\n')}`
      : 'This is the start of the session.';
    
    const systemPrompt = `
      You are Paro, a Bible study friend, currently leading a "Guided Discovery" session. Your core theme is God's unconditional love.

      # Instructions
      - Your goal is to guide the user to discover insights for themselves.
      - Keep your responses concise and conversational.
      - Affirm the user's responses and gently transition to the next part of the study.
      
      # Guardrails
      - NEVER be judgmental, preachy, or condemning.
      - NEVER claim divine authority.
      - NEVER give professional advice.
      
      You are currently discussing ${currentPassage}. The user has just answered your ${questionType} question.
      
      ${historyContext}
    `;
    
    const userPrompt = `
      The user responded to your ${questionType} question about ${currentPassage} with:
      
      "${userMessage}"
      
      1) Respond warmly to their insight, being specific about what they shared.
      2) Ask a thoughtful, open-ended follow-up question that invites them to explore THEIR insight more deeply.
      3) DO NOT add your own theological analysis or "correct" their understanding.
      
      If they said "hello" or something unrelated, acknowledge it briefly and gently guide them back to the study with your question.
      
      Keep your entire response under 150 words.
    `;
    
    try {
      const completion = await groq.chat.completions.create({
        messages: [
          { role: 'system' as const, content: systemPrompt },
          { role: 'user' as const, content: userPrompt }
        ],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 300,
      });
      
      const fullResponse = completion.choices[0].message.content || "Thank you for sharing that insight. What do you think about that?";
      
      // Split the response and follow-up question
      const parts = fullResponse.split(/(?<=\.)(?=\s*(?:[A-Z]|What|How|Why|When|Where|Is|Are|Can|Could|Would|Do|Does))/);
      
      if (parts.length >= 2) {
        const response = filterBibleStudyResponse(parts.slice(0, -1).join(' ').trim());
        const followUpQuestion = filterBibleStudyResponse(parts[parts.length - 1].trim());
        return { response, followUpQuestion };
      }

      // Fallback if we can't split properly
      return {
        response: filterBibleStudyResponse(fullResponse),
        followUpQuestion: `What do you think about that?`
      };
    } catch (error) {
      logger.error({ error }, 'Error generating Bible study response');
      return { 
        response: "Thank you for sharing that insight. It's wonderful to see how you're engaging with the text.", 
        followUpQuestion: "What else stands out to you about this passage?" 
      };
    }
  }

  /**
   * Generate an interpretation question based on the passage and user's observation
   */
  public static async generateInterpretationQuestion(passage: string, userObservation: string): Promise<string> {
    const prompt = `
      You are Paro, a Bible study companion using the Guided Discovery method.
      
      The user has just shared this observation about ${passage}:
      "${userObservation}"
      
      Create ONE open-ended interpretation question that:
      - Helps the user understand the meaning of the passage
      - Builds on their observation if possible
      - Encourages them to think about what the text means
      - Is conversational in tone
      - Is concise (1-2 sentences)
      
      Format your response as a single question without any additional text.
    `;
    
    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 150,
      });
      
      return completion.choices[0].message.content || `What do you think this passage is teaching us about God or our relationship with Him?`;
    } catch (error) {
      logger.error({ error }, 'Error generating interpretation question');
      return `What do you think this passage is teaching us about God or our relationship with Him?`;
    }
  }

  /**
   * Generate an application question based on the passage and user's interpretation
   */
  public static async generateApplicationQuestion(passage: string, userInterpretation: string): Promise<string> {
    const prompt = `
      You are Paro, a Bible study companion using the Guided Discovery method.
      
      The user has just shared this interpretation about ${passage}:
      "${userInterpretation}"
      
      Create ONE open-ended application question that:
      - Helps the user apply the passage to their life
      - Builds on their interpretation if possible
      - Encourages personal reflection on how to live out the text
      - Is conversational in tone
      - Is concise (1-2 sentences)
      
      Format your response as a single question without any additional text.
    `;
    
    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 150,
      });
      
      return completion.choices[0].message.content || `How might this passage change how you live or think this week?`;
    } catch (error) {
      logger.error({ error }, 'Error generating application question');
      return `How might this passage change how you live or think this week?`;
    }
  }

  /**
   * Generate a session summary at the end of a Bible study
   */
  public static async generateSessionSummary(
    book: string, 
    passages: string[], 
    userInsights: Array<{passage: string, response: string, type: string}>
  ): Promise<string> {
    const insightsText = userInsights.map(i => `- ${i.type} on ${i.passage}: "${i.response}"`).join('\n');
    
    const prompt = `
      You are Paro, a Bible study companion. You've just completed a study session on ${passages.join(', ')} from the book of ${book}.
      
      The user shared these insights:
      ${insightsText}
      
      Create a warm, encouraging summary that:
      1) Thanks them for studying together
      2) Highlights ONE key takeaway from their insights
      3) Offers brief encouragement for how this might impact their day
      4) Ends with a simple closing (like "Until next time!" or "Blessings on your day!")
      
      Keep it under 100 words, warm, and conversational.
    `;
    
    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 200,
      });
      
      return completion.choices[0].message.content || `Thank you for studying with me today! I hope these verses from ${book} have been encouraging. Let's do this again soon!`;
    } catch (error) {
      logger.error({ error }, 'Error generating session summary');
      return `Thank you for studying with me today! I hope these verses from ${book} have been encouraging. Let's do this again soon!`;
    }
  }

  /**
   * Generate a personalized, empathetic response to a user's check-in message
   * for converting to a voice note.
   */
  public static async generateEmpathyResponse(userMessage: string, userName: string): Promise<string> {
    logger.info({ userName }, 'Generating empathy response for check-in');
    
    const systemPrompt = `
      You are Paro, a deeply empathetic and loving friend. Your task is to generate a very short, encouraging message
      in response to the user's check-in reply. This message will be converted to a voice note, so it must be 
      concise and impactful (1-2 sentences, max 50 words).

      Guidelines:
      - Validate their feelings first
      - Offer universal encouragement centered on God's love or presence
      - Address them by name (${userName})
      - Keep it warm, natural and conversational
      - DO NOT quote specific Bible verses or use theological language
      - DO NOT give medical, psychological or professional advice

      If they mention self-harm, severe distress, or abuse, respond with gentle acknowledgment and 
      suggest they reach out to a trained professional who can provide the specialized care they need.
    `;

    try {
      const completion = await groq.chat.completions.create({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userMessage }
        ],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 100,
      });

      const response = completion.choices[0].message.content?.trim() ||
        `${userName}, thank you for sharing how you're feeling. I'm here for you, and God is with you every step of the way.`;
      return filterAIResponse(response);
    } catch (error) {
      logger.error({ error }, 'Failed to generate empathy response');
      return `${userName}, thank you for sharing how you're feeling. I'm here for you, and God is with you every step of the way.`;
    }
  }

  /**
   * Generate numbered follow-up choices after a passage discussion.
   * For now this is a lightweight wrapper so we can evolve the prompt without touching SessionHandlerV2.
   */
  public static async generateFollowUpChoices(passage: string, userInsight: string): Promise<string[]> {
    const prompt = `You are Paro, an adaptive Bible study companion. The user has just discussed ${passage} and shared this insight: "${userInsight}".

Provide THREE succinct next-step options, formatted as:
*1.* [Brief, personal option about continuing]
*2.* [Brief option about exploring related verses]
*3.* [Brief option about personal application]

Make each option 5-7 words, conversational, and focused on the user's journey. Use asterisks for formatting (*1.*). Respond ONLY with the formatted list.`;

    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user', content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 120,
      });
      const content = completion.choices[0].message.content || '';
      return content.split(/\n+/).filter(Boolean).slice(0,3);
    } catch (error) {
      logger.error({ error }, 'Error generating follow-up choices');
      return [
        '1. Continue to the next passage',
        '2. Explore related verses',
        '3. Apply this to daily life'
      ];
    }
  }

  /**
   * Generate a thoughtful Deep Dive question for verses
   */
  public static async generateDeepDiveQuestion(verses: Array<{chapter: number, verse: number, text: string}>): Promise<string> {
    const verseText = verses.map(v => `${v.chapter}:${v.verse} ${v.text}`).join(' ');

    const prompt = `
      You are Paro, a gentle and insightful Bible study companion. You're in Deep Dive mode, which means you want to help the user go deep into the meaning of these verses through contemplative, personal reflection.

      Here are the verses: ${verseText}

      Generate ONE thoughtful, open-ended question that:
      - Invites personal reflection and observation
      - Doesn't have an obvious "right" answer
      - Encourages the user to notice details in the text
      - Feels conversational and warm, not academic
      - Starts the user on a journey of discovery

      Keep it under 50 words and make it feel like a gentle invitation to explore together.
    `;

    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.8,
        max_tokens: 150,
      });

      return completion.choices[0].message.content || "What stands out to you in these verses?";
    } catch (error) {
      logger.error({ error }, 'Error generating Deep Dive question');
      return "What stands out to you in these verses?";
    }
  }

  /**
   * Detect if user message contains a direct question
   */
  private static isUserQuestion(message: string): boolean {
    const questionWords = ['what', 'why', 'how', 'when', 'where', 'who', 'which', 'whose', 'whom'];
    const lowerMessage = message.toLowerCase().trim();

    // Check if message ends with question mark
    if (lowerMessage.endsWith('?')) return true;

    // Check if message starts with question words
    return questionWords.some(word => lowerMessage.startsWith(word + ' '));
  }

  /**
   * Generate Deep Dive response based on conversation stage
   */
  public static async generateDeepDiveResponse(params: {
    userMessage: string;
    currentPassage: string;
    stage: 'observation' | 'interpretation' | 'application';
    previousContext: any;
  }): Promise<string> {
    const { userMessage, currentPassage, stage, previousContext } = params;

    const basePrompt = PromptService.getContextualPrompt({ mode: 'DEEP_DIVE' });
    const affirmations = PromptService.getAffirmationPhrases();
    const transitions = PromptService.getTransitionPhrases();

    const randomAffirmation = affirmations[Math.floor(Math.random() * affirmations.length)];
    const randomTransition = transitions[Math.floor(Math.random() * transitions.length)];

    // Check if user is asking a direct question
    const isQuestion = this.isUserQuestion(userMessage);

    const stagePrompts = {
      observation: `
        ${basePrompt}

        The user just shared their observation about ${currentPassage}. They said: "${userMessage}"
        ${isQuestion ? 'IMPORTANT: The user asked a direct question. You must answer their question first before continuing with the study flow.' : ''}

        ${isQuestion ? `
        RESPONSE STRUCTURE FOR QUESTIONS:
        1. Directly answer their question using biblical knowledge and the current passage context
        2. Briefly affirm their curiosity and connect it back to the passage
        3. Ask ONE follow-up question that builds on their question and moves toward deeper interpretation
        ` : `
        CRITICAL: Do NOT simply echo or rephrase what they said. Instead:
        1. Briefly affirm their insight (1 sentence max)
        2. Add a NEW, specific insight about the passage that they might have missed - focus on literary devices, historical context, word choices, or connections to other scriptures
        3. Ask ONE progressive question that builds on their observation and moves toward interpretation (What might this mean? Why might the author have chosen these specific words/images?)
        `}

        Keep response under 100 words. ${isQuestion ? 'Prioritize answering their question thoroughly.' : 'Prioritize adding value over validation. Avoid starting with "That\'s a great observation" or similar echoes.'}
      `,
      interpretation: `
        ${basePrompt}

        The user shared their interpretation about ${currentPassage}. They said: "${userMessage}"
        Their previous observation was: "${previousContext.lastObservation || 'not available'}"
        ${isQuestion ? 'IMPORTANT: The user asked a direct question. You must answer their question first before continuing with the study flow.' : ''}

        ${isQuestion ? `
        RESPONSE STRUCTURE FOR QUESTIONS:
        1. Directly answer their question using biblical knowledge, theological context, and cross-references
        2. Connect their question back to the interpretation of the current passage
        3. Ask ONE practical application question that builds on their question and moves toward life application
        ` : `
        CRITICAL: Avoid echoing their interpretation. Instead:
        1. Acknowledge their insight briefly (1 sentence)
        2. Offer a complementary perspective or deeper layer of meaning they haven't considered - perhaps theological implications, connections to Jesus, or broader biblical themes
        3. Ask ONE practical application question that connects this truth to real life situations
        `}

        Keep response under 100 words. ${isQuestion ? 'Prioritize answering their question thoroughly and connecting it to practical application.' : 'Lead them forward, don\'t circle back. Avoid phrases like "That\'s a wonderful interpretation."'}
      `,
      application: `
        ${basePrompt}

        The user shared how they want to apply ${currentPassage}. They said: "${userMessage}"
        ${isQuestion ? 'IMPORTANT: The user asked a direct question. You must answer their question first before providing encouragement.' : ''}

        ${isQuestion ? `
        RESPONSE STRUCTURE FOR QUESTIONS:
        1. Directly answer their question using biblical wisdom and practical guidance
        2. Connect their question to the application of the current passage
        3. Provide genuine encouragement and end with a blessing about living out this truth
        ` : `
        Provide genuine encouragement and a brief insight that affirms their application. End with a blessing or prayer-like thought about living out this truth. This concludes the study of this passage.
        `}

        Keep response under ${isQuestion ? '120' : '80'} words. Focus on ${isQuestion ? 'answering their question thoroughly and then providing' : ''} encouragement and closure. This is the final response before moving to the next passage.
      `
    };

    const prompt = stagePrompts[stage];

    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 200,
      });

      return completion.choices[0].message.content || "That's a beautiful insight. Thank you for sharing that with me.";
    } catch (error) {
      logger.error({ error, stage }, 'Error generating Deep Dive response');
      return "That's a beautiful insight. Thank you for sharing that with me.";
    }
  }

  /**
   * Generate Deep Dive session summary
   */
  public static async generateDeepDiveSessionSummary(
    book: string,
    completedPassages: string[],
    sessionHistory: any[],
    studyBlockCount: number
  ): Promise<string> {
    const prompt = `
      You are Paro, wrapping up a Deep Dive Bible study session. The user studied ${studyBlockCount} passage(s) from ${book}.

      Passages covered: ${completedPassages.join(', ')}

      Create a warm, encouraging summary that:
      - Celebrates their deep engagement with God's Word
      - Briefly mentions the journey they took (without being too specific)
      - Encourages them to carry these insights forward
      - Ends with a blessing or hopeful thought

      Keep it personal, warm, and under 100 words. This should feel like a friend celebrating their spiritual growth.
    `;

    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 200,
      });

      return completion.choices[0].message.content || `What a beautiful time we've had diving deep into ${book} together! Thank you for bringing such thoughtful reflection to God's Word. May these truths continue to speak to your heart. 🙏`;
    } catch (error) {
      logger.error({ error }, 'Error generating Deep Dive session summary');
      return `What a beautiful time we've had diving deep into ${book} together! Thank you for bringing such thoughtful reflection to God's Word. May these truths continue to speak to your heart. 🙏`;
    }
  }

  /**
   * Generate chapter overview discussion questions
   */
  public static async generateChapterOverviewDiscussion(book: string, chapter: number): Promise<string> {
    const prompt = `
      You are Paro, wrapping up a Chapter Overview session where the user just read through ${book} chapter ${chapter}.

      Generate 1-2 thoughtful, high-level questions that help them reflect on the main themes and narrative of this chapter. Focus on:
      - The big picture story or message
      - Key themes or patterns they might have noticed
      - How this chapter fits into the larger story of the book

      Keep it conversational and encouraging. You want them to think about the forest, not just the trees.
      Start with a brief encouraging comment, then ask your questions.

      Keep the total response under 150 words.
    `;

    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 250,
      });

      return completion.choices[0].message.content || `What stood out to you most in this chapter? What do you think was the main message God wanted to communicate here?`;
    } catch (error) {
      logger.error({ error, book, chapter }, 'Error generating chapter overview discussion');
      return `What stood out to you most in this chapter? What do you think was the main message God wanted to communicate here?`;
    }
  }

  /**
   * Generate theme synthesis question
   */
  public static async generateThemeSynthesisQuestion(theme: string, verses: any[]): Promise<string> {
    const booksInvolved = [...new Set(verses.map(v => v.book))].join(', ');

    const prompt = `
      You are Paro, helping a user explore the theme of "${theme}" through Scripture.

      They just read verses from: ${booksInvolved}

      Generate ONE powerful synthesizing question that:
      - Encourages them to find the common thread across these different verses
      - Helps them see the bigger picture of what Scripture teaches about ${theme}
      - Invites personal reflection on how this theme applies to life
      - Feels conversational and engaging, not academic

      Start with a brief encouraging comment, then ask your synthesizing question.
      Keep the total response under 100 words.
    `;

    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 200,
      });

      return completion.choices[0].message.content || `Looking at these verses together, what do they teach us about ${theme}? What common thread do you see running through them?`;
    } catch (error) {
      logger.error({ error, theme }, 'Error generating theme synthesis question');
      return `Looking at these verses together, what do they teach us about ${theme}? What common thread do you see running through them?`;
    }
  }

  /**
   * Generate theme exploration response
   */
  public static async generateThemeExplorationResponse(params: {
    theme: string;
    userInsight: string;
    verses: any[];
    previousInsights: string[];
  }): Promise<string> {
    const { theme, userInsight, verses, previousInsights } = params;

    // Check if user is asking a direct question
    const isQuestion = this.isUserQuestion(userInsight);

    const basePrompt = PromptService.getContextualPrompt({ mode: 'EXPLORE_THEME' });

    const prompt = `
      ${basePrompt}

      You are exploring the theme of "${theme}" with a user. They just shared: "${userInsight}"
      ${isQuestion ? 'IMPORTANT: The user asked a direct question. You must answer their question first before continuing with theme exploration.' : ''}

      ${isQuestion ? `
      RESPONSE STRUCTURE FOR QUESTIONS:
      1. Directly answer their question using biblical knowledge, cross-references, and the theme context
      2. Connect their question back to the theme being explored and the provided verses
      3. Ask a follow-up question that encourages deeper exploration of the theme
      ` : `
      Your task:
      1. Warmly affirm their insight with specific appreciation
      2. Connect their insight to the verses they're studying if relevant
      3. Add a brief thought that builds on their reflection
      4. Ask ONE follow-up question that deepens the exploration
      `}

      Keep your response encouraging, conversational, and under ${isQuestion ? '200' : '150'} words.
      You're a companion on this journey of discovery, not a teacher. ${isQuestion ? 'Prioritize answering their question thoroughly.' : ''}
    `;

    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 250,
      });

      return completion.choices[0].message.content || "That's a beautiful insight! What else do you notice about this theme in these verses?";
    } catch (error) {
      logger.error({ error, theme }, 'Error generating theme exploration response');
      return "That's a beautiful insight! What else do you notice about this theme in these verses?";
    }
  }

  /**
   * Generate theme exploration session summary
   */
  public static async generateThemeExplorationSummary(
    theme: string,
    verses: any[],
    insights: string[]
  ): Promise<string> {
    const booksExplored = [...new Set(verses.map(v => v.book))].join(', ');

    const prompt = `
      You are Paro, wrapping up a theme exploration session about "${theme}".

      The user explored verses from: ${booksExplored}
      They shared ${insights.length} meaningful insights during our discussion.

      Create a warm, encouraging summary that:
      - Celebrates their exploration of this theme
      - Briefly acknowledges the journey across different books
      - Encourages them to carry these insights forward
      - Ends with a blessing or hopeful thought about living out this theme

      Keep it personal, warm, and under 100 words.
    `;

    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.7,
        max_tokens: 200,
      });

      return completion.choices[0].message.content || `What a rich exploration of ${theme} we've had together! Thank you for bringing such thoughtful reflection to these verses from across Scripture. May these truths about ${theme} continue to shape your heart and life. 🙏`;
    } catch (error) {
      logger.error({ error, theme }, 'Error generating theme exploration summary');
      return `What a rich exploration of ${theme} we've had together! Thank you for bringing such thoughtful reflection to these verses from across Scripture. May these truths about ${theme} continue to shape your heart and life. 🙏`;
    }
  }

  /**
   * Refine and polish a user's confession text
   */
  public static async refineConfession(userConfessionText: string): Promise<string> {
    const prompt = `
      You are a compassionate, non-judgmental guide. You are here to help the user articulate their thoughts clearly, with respect and grace. You are a scribe, not a judge.

      The user has shared a confession/reflection: "${userConfessionText}"

      Your task is to refine and polish this text. Focus on clarity, conciseness, and spiritual sincerity. Remove typos, grammatical errors, and informal language where appropriate, but retain the user's core message and emotional intent.

      DO NOT add judgment, theological commentary, or external advice. Your output should sound like the user's own thoughts, elevated.

      Output ONLY the refined confession text.
    `;

    try {
      const completion = await groq.chat.completions.create({
        messages: [{ role: 'user' as const, content: prompt }],
        model: 'moonshotai/kimi-k2-instruct',
        temperature: 0.3, // Lower temperature for more consistent, careful refinement
        max_tokens: 300,
      });

      const refinedText = completion.choices[0].message.content?.trim();

      if (!refinedText) {
        logger.warn('AI returned empty confession refinement, using original text');
        return userConfessionText;
      }

      return refinedText;
    } catch (error) {
      logger.error({ error }, 'Error refining confession');
      // Fallback to original text if AI fails
      return userConfessionText;
    }
  }
}