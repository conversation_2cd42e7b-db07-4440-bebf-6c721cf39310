// src/services/StateManager.ts
import { logger } from '../config/logger.config';
import { pool } from '../database/client';
import { User, UserSchema, Confession, ConfessionSchema } from '../types/index';
import { nextLocalCheckIn } from '../utils/TimeUtils';

export class StateManager {
  /**
   * Atomically finds a user by their hash or creates them if they don't exist.
   * This robust version separates the write (UPSERT) and read (SELECT) for guaranteed consistency.
   * @param userHash - The hashed phone number of the user.
   * @returns The full, type-safe user object from the database.
   */
  public static async findOrCreateUser(userHash: string, jid: string): Promise<User> {

    const upsertQuery = `
      INSERT INTO users (user_hash, jid, last_interaction_at)
      VALUES ($1, $2, NOW())
      ON CONFLICT (user_hash)
      DO UPDATE SET jid = EXCLUDED.jid,
      last_interaction_at = NOW();
    `;
    const selectQuery = 'SELECT * FROM users WHERE user_hash = $1';


    try {
      await pool.query(upsertQuery, [userHash, jid]); ;
      const { rows } = await pool.query(selectQuery, [userHash]);
      const user = UserSchema.parse(rows[0]); // Validate the final data

      if (!user.is_active) {
        throw new Error('User account is inactive.');
      }
      return user;
    } catch (error) {
      logger.error({ userHash, error }, 'Database error in findOrCreateUser');
      throw error;
    }
  }
  
  public static async updateOnboardingStep(userId: string, step: User['onboarding_step']) {
    const query = 'UPDATE users SET onboarding_step = $1 WHERE id = $2';
    await pool.query(query, [step, userId]);
    logger.info({ userId, step }, 'Updated onboarding_step');
  }

  public static async updateUserName(userId: string, name: string) {
    const query = 'UPDATE users SET name = $1 WHERE id = $2';
    await pool.query(query, [name, userId]);
    logger.info({ userId }, 'Updated user name'); // Note: We don't log the name itself for privacy
  }

  public static async updateUserReminder(userId: string, time: string) {
    const query = 'UPDATE users SET reminder_time_pref = $1 WHERE id = $2';
    await pool.query(query, [time, userId]);
    logger.info({ userId, time }, 'Updated user reminder time');
  }

  public static async updateUserTimezone(userId: string, timezone: string) {
    const query = 'UPDATE users SET user_timezone = $1 WHERE id = $2';
    await pool.query(query, [timezone, userId]);
    logger.info({ userId, timezone }, 'Updated user timezone');
  }

  public static async getUsersForReminders(): Promise<User[]> {
    // Fetches all active users who have set a reminder time and timezone.
    const query = `
      SELECT *
      FROM users
      WHERE is_active = true
        AND next_reminder_at IS NOT NULL
        AND next_reminder_at <= NOW()
        AND jid IS NOT NULL
        AND user_timezone IS NOT NULL;
    `;
    const { rows } = await pool.query(query);
    return rows.map(row => UserSchema.parse(row));
  }

  public static async scheduleNextReminder(userId: string, nextTime: Date) {
    const q = 'UPDATE users SET next_reminder_at = $1 WHERE id = $2';
    await pool.query(q, [nextTime, userId]);
  }

  public static async updateUserJourney(userId: string, book: string) {
    const query = `
      UPDATE users
      SET current_book = $1,
          last_completed_chapter = 0,
          last_completed_verse   = 0
      WHERE id = $2`;
    await pool.query(query, [book, userId]);
    logger.info({ userId, book }, 'Updated user journey');
  }

  /**
   * Marks a user as verified after they provide the correct keyword.
   */
  public static async markUserVerified(userId: string): Promise<void> {
    const query = 'UPDATE users SET is_verified = true WHERE id = $1';
    await pool.query(query, [userId]);
    logger.info({ userId }, 'User marked as verified');
  }

  /**
   * Finds users due for reminders and atomically updates their next reminder time
   * @returns Array of users who need reminders
   */
  public static async findAndResetDueReminders(now: Date): Promise<User[]> {
    const query = `
      UPDATE users
      SET next_reminder_at = next_reminder_at + INTERVAL '1 day'
      WHERE is_active = true AND next_reminder_at IS NOT NULL AND next_reminder_at <= $1
      RETURNING *;
    `;
    const { rows } = await pool.query(query, [now]);
    if (rows.length > 0) {
      logger.info(`Found ${rows.length} users due for a reminder.`);
    }
    return rows.map(row => UserSchema.parse(row));
  }

  /**
   * Finds users due for check-ins and atomically updates their next check-in time
   * Sets next check-in to a random time tomorrow between 10AM and 8PM in user's timezone
   * @returns Array of users who need check-ins
   */
  public static async findAndResetDueCheckIns(now: Date): Promise<User[]> {
    const query = `
      UPDATE users
      SET next_check_in_at = (
        CURRENT_DATE + INTERVAL '1 day' + 
        INTERVAL '10 hours' + 
        (RANDOM() * (10 * 60)) * INTERVAL '1 minute'
      )
      WHERE is_active = true AND next_check_in_at IS NOT NULL AND next_check_in_at <= $1
      RETURNING *;
    `;
    const { rows } = await pool.query(query, [now]);
    if (rows.length > 0) {
      logger.info(`Found ${rows.length} users due for a check-in.`);
    }
    return rows.map(row => UserSchema.parse(row));
  }

  /**
   * Schedule initial check-in for a user at random time tomorrow between 10AM-8PM
   */
  public static async scheduleInitialCheckIn(userId: string): Promise<void> {
    const tzQuery = 'SELECT user_timezone FROM users WHERE id = $1';
    const { rows } = await pool.query(tzQuery, [userId]);
    if (rows.length === 0) return;
    const tz = rows[0].user_timezone as string | null;
    if (!tz) return;
    const nextTime = nextLocalCheckIn(tz);
    const query = 'UPDATE users SET next_check_in_at = $1 WHERE id = $2';
    await pool.query(query, [nextTime, userId]);
    logger.info({ userId, nextTime }, 'Scheduled initial check-in');
  }

  /**
   * Schedule the *next* check-in after one has fired.
   */
  public static async scheduleNextCheckIn(userId: string, userTz: string): Promise<void> {
    const nextTime = nextLocalCheckIn(userTz);
    await pool.query('UPDATE users SET next_check_in_at = $1 WHERE id = $2', [nextTime, userId]);
    logger.info({ userId, nextTime }, 'Scheduled next check-in');
  }

  /**
   * Atomically claim users whose check-in time has arrived.
   * We set next_check_in_at = NULL to avoid double processing; caller must scheduleNextCheckIn afterwards.
   */
  public static async claimDueCheckIns(now: Date): Promise<User[]> {
    const query = `
      UPDATE users
      SET next_check_in_at = NULL
      WHERE next_check_in_at IS NOT NULL AND next_check_in_at <= $1 AND is_active = true
      RETURNING *;
    `;
    const { rows } = await pool.query(query, [now]);
    if (rows.length) logger.info({ count: rows.length }, 'Claimed users for check-in');
    return rows.map(r => UserSchema.parse(r));
  }

  /**
   * Atomically fetches the next batch of verses for a user and updates their progress within a transaction.
   */
  public static async getNextStudyBlock(userId: string, book: string, chapter: number, verse: number, limit: number) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const verseQuery = `
        SELECT id, book, chapter, verse, text FROM rag_content
        WHERE book = $1 AND ( (chapter = $2 AND verse > $3) OR chapter > $2 )
        ORDER BY chapter ASC, verse ASC LIMIT $4;
      `;
      const { rows: nextVerses } = await client.query(verseQuery, [book, chapter, verse, limit]);

      if (nextVerses.length > 0) {
        const lastVerseInBatch = nextVerses[nextVerses.length - 1];
        const progressQuery = 'UPDATE users SET last_completed_chapter = $1, last_completed_verse = $2 WHERE id = $3';
        await client.query(progressQuery, [lastVerseInBatch.chapter, lastVerseInBatch.verse, userId]);
      }

      await client.query('COMMIT');
      logger.info({ userId, book, count: nextVerses.length }, 'Fetched next study block and updated progress.');
      return nextVerses;
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error({ userId, error }, 'Failed to get next study block; transaction rolled back.');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Update user's preferred study mode
   */
  public static async updatePreferredStudyMode(userId: string, studyMode: string): Promise<void> {
    const query = 'UPDATE users SET preferred_study_mode = $1 WHERE id = $2';
    await pool.query(query, [studyMode, userId]);
    logger.info({ userId, studyMode }, 'Updated user preferred study mode');
  }

  /**
   * Update user's last used study mode and increment session count
   */
  public static async updateStudyModeUsage(userId: string, studyMode: string): Promise<void> {
    const query = `
      UPDATE users
      SET last_study_mode_used = $1,
          total_study_sessions = total_study_sessions + 1
      WHERE id = $2
    `;
    await pool.query(query, [studyMode, userId]);
    logger.info({ userId, studyMode }, 'Updated study mode usage');
  }

  /**
   * Update user's last explored theme
   */
  public static async updateLastThemeExplored(userId: string, theme: string): Promise<void> {
    const query = 'UPDATE users SET last_theme_explored = $1 WHERE id = $2';
    await pool.query(query, [theme, userId]);
    logger.info({ userId, theme }, 'Updated last theme explored');
  }

  /**
   * Find a user by their JID (WhatsApp identifier)
   * @param jid - The WhatsApp JID of the user
   * @returns The user object if found, null otherwise
   */
  public static async findUserByJid(jid: string): Promise<User | null> {
    const query = 'SELECT * FROM users WHERE jid = $1';
    try {
      const { rows } = await pool.query(query, [jid]);
      if (rows.length === 0) {
        return null;
      }
      return UserSchema.parse(rows[0]);
    } catch (error) {
      logger.error({ jid, error }, 'Database error in findUserByJid');
      throw error;
    }
  }

  /**
   * Get user's study preferences and history
   */
  public static async getUserStudyPreferences(userId: string): Promise<{
    preferredStudyMode: string | null;
    lastStudyModeUsed: string | null;
    totalStudySessions: number;
    lastThemeExplored: string | null;
  }> {
    const query = `
      SELECT preferred_study_mode, last_study_mode_used,
             total_study_sessions, last_theme_explored
      FROM users WHERE id = $1
    `;
    const { rows } = await pool.query(query, [userId]);

    if (rows.length === 0) {
      return {
        preferredStudyMode: null,
        lastStudyModeUsed: null,
        totalStudySessions: 0,
        lastThemeExplored: null
      };
    }

    return {
      preferredStudyMode: rows[0].preferred_study_mode,
      lastStudyModeUsed: rows[0].last_study_mode_used,
      totalStudySessions: rows[0].total_study_sessions || 0,
      lastThemeExplored: rows[0].last_theme_explored
    };
  }

  // ==================== CONFESSION MANAGEMENT ====================

  /**
   * Save a new confession for a user
   */
  public static async saveConfession(
    userId: string,
    originalText: string,
    refinedText: string,
    reminderTime?: string,
    reminderFrequency: string = 'once'
  ): Promise<Confession> {
    const query = `
      INSERT INTO confessions (user_id, original_text, refined_text, reminder_time, reminder_frequency)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `;

    const { rows } = await pool.query(query, [userId, originalText, refinedText, reminderTime, reminderFrequency]);
    const confession = ConfessionSchema.parse(rows[0]);

    logger.info({ userId, confessionId: confession.id }, 'Saved new confession');
    return confession;
  }

  /**
   * Get active confession for a user
   */
  public static async getUserConfession(userId: string): Promise<Confession | null> {
    const query = `
      SELECT * FROM confessions
      WHERE user_id = $1 AND is_active = true
      ORDER BY created_at DESC
      LIMIT 1
    `;

    const { rows } = await pool.query(query, [userId]);
    if (rows.length === 0) return null;

    return ConfessionSchema.parse(rows[0]);
  }

  /**
   * Update an existing confession
   */
  public static async updateConfession(
    confessionId: string,
    originalText: string,
    refinedText: string
  ): Promise<void> {
    const query = `
      UPDATE confessions
      SET original_text = $1, refined_text = $2, updated_at = NOW()
      WHERE id = $3
    `;

    await pool.query(query, [originalText, refinedText, confessionId]);
    logger.info({ confessionId }, 'Updated confession');
  }

  /**
   * Schedule confession reminder
   */
  public static async scheduleConfessionReminder(
    confessionId: string,
    nextReminderAt: Date
  ): Promise<void> {
    const query = `
      UPDATE confessions
      SET next_reminder_at = $1, updated_at = NOW()
      WHERE id = $2
    `;

    await pool.query(query, [nextReminderAt, confessionId]);
    logger.info({ confessionId, nextReminderAt }, 'Scheduled confession reminder');
  }

  /**
   * Get confessions due for reminders
   */
  public static async getConfessionsDueForReminder(now: Date): Promise<Array<{
    confession: Confession;
    user: Pick<User, 'id' | 'jid' | 'name' | 'user_timezone'>;
  }>> {
    const query = `
      SELECT c.*, u.id as user_id, u.jid, u.name, u.user_timezone
      FROM confessions c
      JOIN users u ON c.user_id = u.id
      WHERE c.is_active = true
        AND c.next_reminder_at IS NOT NULL
        AND c.next_reminder_at <= $1
        AND u.is_active = true
        AND u.jid IS NOT NULL
    `;

    const { rows } = await pool.query(query, [now]);

    return rows.map(row => ({
      confession: ConfessionSchema.parse({
        id: row.id,
        user_id: row.user_id,
        original_text: row.original_text,
        refined_text: row.refined_text,
        reminder_time: row.reminder_time,
        reminder_frequency: row.reminder_frequency,
        next_reminder_at: row.next_reminder_at,
        is_active: row.is_active,
        created_at: row.created_at,
        updated_at: row.updated_at
      }),
      user: {
        id: row.user_id,
        jid: row.jid,
        name: row.name,
        user_timezone: row.user_timezone
      }
    }));
  }

  /**
   * Deactivate all active confession reminders for a user
   * This ensures only one confession reminder is active per user at a time
   */
  public static async deactivateUserConfessionReminders(userId: string): Promise<void> {
    const query = `
      UPDATE confessions
      SET next_reminder_at = NULL, updated_at = NOW()
      WHERE user_id = $1
        AND is_active = true
        AND next_reminder_at IS NOT NULL
    `;

    const result = await pool.query(query, [userId]);
    logger.info({
      userId,
      deactivatedCount: result.rowCount
    }, 'Deactivated existing confession reminders for user');
  }

  /**
   * Mark confession reminder as sent and schedule next one if recurring
   */
  public static async markConfessionReminderSent(confessionId: string): Promise<void> {
    // For now, just clear the next_reminder_at since we're implementing "once" reminders
    // This can be extended later for recurring reminders
    const query = `
      UPDATE confessions
      SET next_reminder_at = NULL, updated_at = NOW()
      WHERE id = $1
    `;

    await pool.query(query, [confessionId]);
    logger.info({ confessionId }, 'Marked confession reminder as sent');
  }
}