// src/utils/ChoiceParser.ts
// Utility to robustly extract a numeric choice (1-9) from a user's free-form SMS.
// Keeps logic isolated so SessionHandlerV2 remains clean.

export function parseChoice(input: string, max: number): number | null {
  if (!input) return null;
  // Normalise
  const cleaned = input
    .trim()
    .toLowerCase()
    .replace(/[^0-9]/g, '') // strip non-digits
    .slice(0, 2); // in case of long numbers

  if (!cleaned) return null;
  const num = parseInt(cleaned, 10);
  if (isNaN(num) || num < 1 || num > max) return null;
  return num;
} 