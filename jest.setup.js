// jest.setup.js
// ---- minimal env so validateEnv() succeeds ----
process.env.NODE_ENV                = 'test';
process.env.PORT                    = '3000';
process.env.DATABASE_URL            = 'http://localhost';
process.env.REDIS_URL               = 'redis://localhost:6379';
process.env.EVOLUTION_API_URL       = 'http://localhost';
process.env.EVOLUTION_API_KEY       = 'test';
process.env.EVOLUTION_WEBHOOK_SECRET= 'test';
process.env.GROQ_API_KEY            = 'test';
process.env.VOYAGE_AI_API_KEY       = 'test';
process.env.EVOLUTION_INSTANCE_NAME = 'test';

// ---- replace real Redis with in-memory mock ----
jest.mock('./src/queue/connection', () => {
  const RedisMock = require('ioredis-mock');
  return { redisConnection: new RedisMock() };
});