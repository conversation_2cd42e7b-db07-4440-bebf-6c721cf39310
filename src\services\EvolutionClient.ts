import axios from 'axios';
import { validateEnv } from '../config/env.config';
import { logger } from '../config/logger.config';

const env = validateEnv();

const evolutionApi = axios.create({
  baseURL: env.EVOLUTION_API_URL,
  headers: {
    'apikey': env.EVOLUTION_API_KEY,
    'Content-Type': 'application/json',
  },
});

export class EvolutionClient {
  public static async sendTextMessage(toJid: string, text: string) {
    const instanceName = env.EVOLUTION_INSTANCE_NAME;
    const url = `/message/sendText/${instanceName}`;

    try {
      logger.info(`Sending message to ${toJid}: "${text}"`);

      // This payload is our best guess based on the docs.
      // The detailed error log will tell us if any field is wrong.
      const payload = {
        number: toJid,
        text,
      };

      const response = await evolutionApi.post(url, payload);
      logger.info({ key: response.data.key }, '✅ Message sent successfully');
      return response.data;

    } catch (error) {
      // THIS IS THE CRITICAL PART THAT REVEALS THE REAL ERROR
      if (axios.isAxiosError(error) && error.response) {
        logger.error(
          {
            status: error.response.status,
            // This line prints the contents of the `[Array]`
            data: error.response.data,
          },
          'Evolution API validation error'
        );
      } else {
        logger.error({ err: error }, 'Unknown error sending message');
      }
      throw error;
    }
  }


  public static async sendPresence(
    toJid: string,
    presence: 'composing' | 'paused'
  ) {
    const instanceName = env.EVOLUTION_INSTANCE_NAME;
    const url = `/chat/sendPresence/${instanceName}`;
    try {
      // Evolution "sendPresence" spec → wrap inside options
      const payload = {
        number: toJid,
        options: {
          presence,       // 'composing' | 'paused'
          delay: 1200,    // ms – tweak or omit
        },
      };

      await evolutionApi.post(url, payload);
      logger.info({ toJid, presence }, 'Sent presence update.');
    } catch (error) { /* ... error logging ... */ }
  }

  /**
   * Sends an audio message (voice note) to a user
   * @param toJid - The WhatsApp JID to send the audio to
   * @param audioUrl - The public URL of the audio file (from R2)
   */
  public static async sendAudioMessage(toJid: string, audioUrl: string) {
    const instanceName = env.EVOLUTION_INSTANCE_NAME;
    const url = `/message/sendWhatsAppAudio/${instanceName}`;

    try {
      logger.info({ toJid }, 'Sending audio message');

      const payload = {
        number: toJid,
        audio: audioUrl,
        options: {
          delay: 1200
        }
      };

      const response = await evolutionApi.post(url, payload);
      logger.info({ key: response.data.key }, '✅ Audio message sent successfully');
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        logger.error(
          {
            status: error.response.status,
            data: error.response.data,
          },
          'Evolution API error sending audio'
        );
      } else {
        logger.error({ error }, 'Unknown error sending audio message');
      }
      throw error;
    }
  }
}