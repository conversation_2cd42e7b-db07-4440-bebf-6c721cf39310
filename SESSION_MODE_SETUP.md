# Session Mode Lock-in System Setup Guide

## Overview

The Session Mode Lock-in system transforms the Bible study experience from a repetitive Q&A loop to a focused, adaptive, and continuous journey. Users choose their study method at the start of each session, and <PERSON><PERSON> acts as an expert guide within that mode.

## Features

### 🎯 Three Study Modes

1. **Deep Dive** - Close, reflective reading of 3-5 verses with contemplative discussion
2. **Chapter Overview** - Reading entire chapters with dynamic chunking for mobile readability
3. **Explore a Theme** - Cross-biblical exploration of specific topics with curated verses

### 🧠 Enhanced Memory System

- **Positional Memory** (Postgres) - Tracks reading progress and study preferences
- **Reflective Memory** (Redis) - Stores insights and conversation history
- **Session Scratchpad** (JSONB) - Maintains current session context

### 🎨 Intelligent Welcome System

- Personalized greetings based on past insights
- Study mode recommendations based on history
- Progress encouragement and context awareness

## Setup Instructions

### 1. Database Migration

Run the database migration to add new schema elements:

```bash
# Development
npm run db:migrate-session-mode

# Production
npm run db:migrate-session-mode:prod
```

This adds:
- New study_step enum values for session modes
- Enhanced user columns for study preferences
- Performance indexes for better query speed

### 2. Environment Configuration

Update your `.env` file with feature flags (copy from `.env.example`):

```env
# Session Mode Lock-in System - ENABLED by default
ENABLE_SESSION_MODE_LOCK_IN=true

# Individual study modes - all ENABLED by default
ENABLE_DEEP_DIVE_MODE=true
ENABLE_CHAPTER_OVERVIEW_MODE=true
ENABLE_EXPLORE_THEME_MODE=true

# Memory and welcome features - ENABLED by default
ENABLE_INTELLIGENT_WELCOME=true
ENABLE_ENHANCED_MEMORY=true

# Dynamic chunking for chapter overview - ENABLED by default
ENABLE_DYNAMIC_CHUNKING=true

# Advanced system prompts - ENABLED by default
ENABLE_ADVANCED_PROMPTS=true
```

### 3. Test the Integration

Run the comprehensive integration test:

```bash
# Development
npm run test:session-mode

# Production
npm run test:session-mode:prod
```

This tests:
- Feature flag configuration
- Mode selection and parsing
- Welcome message generation
- Memory service enhancements
- Database schema compatibility
- Error handling
- Performance benchmarks

### 4. Gradual Rollout (Optional)

You can enable features gradually by setting specific flags to `false`:

```env
# Enable only Deep Dive mode initially
ENABLE_DEEP_DIVE_MODE=true
ENABLE_CHAPTER_OVERVIEW_MODE=false
ENABLE_EXPLORE_THEME_MODE=false
```

### 5. Monitoring and Logging

The system includes comprehensive logging for:
- Session mode selections
- Study progress tracking
- Memory operations
- Performance metrics
- Error handling

Monitor logs for:
```
'Started new session mode lock-in session'
'Initialized session with study mode'
'Completed [MODE] session'
```

## User Experience Flow

### New Session Flow

1. **Welcome Message** - Personalized greeting with past insight reference
2. **Mode Recommendation** - AI-suggested mode based on history (optional)
3. **Mode Selection** - Clear menu with three study options
4. **Mode Lock-in** - Session operates exclusively within chosen mode
5. **Adaptive Interaction** - Mode-specific conversation patterns
6. **Natural Conclusion** - Graceful session ending with summary

### Deep Dive Mode

- **Study Blocks**: 3-5 verses presented with thoughtful questions
- **Two-Step Flow**: Observation → Interpretation → Application
- **Sustained Focus**: No interruptions from choice menus
- **Natural Progression**: Seamless transition between passages

### Chapter Overview Mode

- **Dynamic Chunking**: Intelligent text splitting (700-1000 characters)
- **Sequential Delivery**: Automatic chunk delivery with 3-second delays
- **Natural Breaks**: Respects paragraph and verse boundaries
- **Big Picture Discussion**: High-level reflection after complete chapter

### Explore a Theme Mode

- **Theme Selection**: User chooses topic of interest
- **Cross-Biblical Search**: RAG-powered verse curation (3-5 verses)
- **Multi-Book Perspective**: Verses from different books for rich context
- **Synthesizing Questions**: AI-generated questions to find common threads

## Technical Architecture

### Core Services

- **SessionModeService** - Main orchestration and mode management
- **WelcomeService** - Intelligent welcome message generation
- **DeepDiveModeHandler** - Deep Dive mode implementation
- **ChapterOverviewModeHandler** - Chapter Overview with dynamic chunking
- **ExploreThemeModeHandler** - Theme exploration implementation
- **PromptService** - Centralized prompt management

### Memory Architecture

- **StateManager** - Enhanced with study preference methods
- **MemoryService** - Extended with theme exploration and progress tracking
- **SessionService** - Updated with study mode support

### Feature Flag System

- **features.config.ts** - Centralized feature flag management
- **Granular Control** - Individual mode and feature toggles
- **Default Enabled** - New features enabled by default as requested

## Backward Compatibility

- **Seamless Fallback** - Original system remains functional when flags are disabled
- **Existing Users** - Continue with current flow until starting new session
- **Data Preservation** - All existing user data and progress maintained
- **API Compatibility** - No breaking changes to external interfaces

## Performance Considerations

- **Optimized Queries** - New indexes for study mode and preference queries
- **Efficient Chunking** - Dynamic text splitting optimized for mobile
- **Memory Management** - Redis TTL and cleanup for session data
- **Async Operations** - Non-blocking message delivery and processing

## Troubleshooting

### Common Issues

1. **Feature flags not working** - Check `.env` file configuration
2. **Database errors** - Run migration script: `npm run db:migrate-session-mode`
3. **Memory issues** - Verify Redis connection and TTL settings
4. **Mode selection not working** - Check feature flag: `ENABLE_SESSION_MODE_LOCK_IN`

### Debug Commands

```bash
# Test specific components
npm run test:session-mode

# Check database schema
npm run db:status

# View logs
tail -f logs/app.log | grep "session mode"
```

## Support

For issues or questions about the Session Mode Lock-in system:

1. Check the integration test results
2. Review the comprehensive logging output
3. Verify feature flag configuration
4. Test with a clean user session

The system is designed to be robust, with fallback mechanisms and comprehensive error handling to ensure a smooth user experience.
