// src/scripts/migrateSessionModeSchema.ts
// Migration script for Session Mode Lock-in system database changes

import 'dotenv/config';
import { pool } from '../database/client';
import { logger } from '../config/logger.config';

async function migrateSessionModeSchema() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    logger.info('Starting Session Mode Lock-in schema migration...');
    
    // 1. Add new study_step enum values
    logger.info('Adding new study_step enum values...');
    await client.query(`
      DO $$
      BEGIN
        ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'AWAITING_MODE_SELECTION';
        ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'DEEP_DIVE_ACTIVE';
        ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CHAPTER_OVERVIEW_ACTIVE';
        ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CHAPTER_OVERVIEW_WAITING';
        ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'EXPLORE_THEME_ACTIVE';
        ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'EXPLORE_THEME_WAITING';
      EXCEPTION WHEN duplicate_object THEN 
        RAISE NOTICE 'study_step enum values already exist';
      END $$;
    `);
    
    // 2. Add new user columns for enhanced memory
    logger.info('Adding enhanced memory columns to users table...');
    
    // Helper function to add column if not exists
    await client.query(`
      CREATE OR REPLACE FUNCTION add_column_if_not_exists(p_table_name TEXT, p_column_name TEXT, p_column_definition TEXT)
      RETURNS VOID AS $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = p_table_name AND column_name = p_column_name) THEN
          EXECUTE 'ALTER TABLE ' || quote_ident(p_table_name) || ' ADD COLUMN ' || quote_ident(p_column_name) || ' ' || p_column_definition;
          RAISE NOTICE 'Added column % to table %', p_column_name, p_table_name;
        ELSE
          RAISE NOTICE 'Column % already exists in table %', p_column_name, p_table_name;
        END IF;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    // Add new columns
    await client.query(`SELECT add_column_if_not_exists('users', 'preferred_study_mode', 'TEXT')`);
    await client.query(`SELECT add_column_if_not_exists('users', 'total_study_sessions', 'INT NOT NULL DEFAULT 0')`);
    await client.query(`SELECT add_column_if_not_exists('users', 'last_study_mode_used', 'TEXT')`);
    await client.query(`SELECT add_column_if_not_exists('users', 'last_theme_explored', 'TEXT')`);
    
    // 3. Ensure study_mode column exists in study_sessions (should already exist)
    logger.info('Ensuring study_mode column exists in study_sessions...');
    await client.query(`SELECT add_column_if_not_exists('study_sessions', 'study_mode', 'TEXT DEFAULT ''DEEP_DIVE''')`);
    
    // 4. Create indexes for better performance
    logger.info('Creating performance indexes...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_users_study_mode 
      ON users (preferred_study_mode) 
      WHERE preferred_study_mode IS NOT NULL;
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_sessions_study_mode 
      ON study_sessions (study_mode) 
      WHERE study_mode IS NOT NULL;
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_sessions_step_mode 
      ON study_sessions (session_step, study_mode) 
      WHERE session_step IS NOT NULL AND study_mode IS NOT NULL;
    `);
    
    // 5. Update existing sessions to have default study mode if null
    logger.info('Updating existing sessions with default study mode...');
    const { rowCount } = await client.query(`
      UPDATE study_sessions 
      SET study_mode = 'DEEP_DIVE' 
      WHERE study_mode IS NULL AND session_type = 'BIBLE_STUDY';
    `);
    
    if (rowCount && rowCount > 0) {
      logger.info(`Updated ${rowCount} existing sessions with default study mode`);
    }
    
    await client.query('COMMIT');
    logger.info('Session Mode Lock-in schema migration completed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error({ error }, 'Session Mode Lock-in schema migration failed');
    throw error;
  } finally {
    client.release();
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrateSessionModeSchema()
    .then(() => {
      logger.info('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error({ error }, 'Migration script failed');
      process.exit(1);
    });
}

export { migrateSessionModeSchema };
