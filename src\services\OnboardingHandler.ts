// src/services/OnboardingHandler.ts
import * as chrono from 'chrono-node';
import { messageQueue } from '../queue/messageQueue';
import { User } from '../types';
import { StateManager } from './StateManager';
import { logger } from '../config/logger.config';
import { DateTime } from 'luxon';
import { containsAccessCode } from '../config/auth.config';
import { ensureMinTypingDelay } from '../utils/messagePacer';
import { pool } from '../database/client';

const countryToTz: Record<string, string> = {
  nigeria: 'Africa/Lagos', 'ng': 'Africa/Lagos', uk: 'Europe/London',
  'united kingdom': 'Europe/London', usa: 'America/New_York', us: 'America/New_York',
  america: 'America/New_York',
};

/**
 * Get available Bible books from the database
 */
async function getAvailableBooks(): Promise<string[]> {
  try {
    const query = 'SELECT DISTINCT book FROM rag_content ORDER BY book ASC';
    const { rows } = await pool.query(query);
    return rows.map(row => row.book);
  } catch (error) {
    logger.error({ error }, 'Failed to get available books');
    // Return default books if database query fails
    return ['Genesis', 'Exodus', 'Psalms', 'Proverbs', 'Matthew', 'Mark', 'Luke', 'John', 'Romans', 'Ephesians'];
  }
}

/**
 * Validate if a book name is available in the database
 */
async function isValidBook(bookName: string): Promise<boolean> {
  try {
    const query = 'SELECT 1 FROM rag_content WHERE book ILIKE $1 LIMIT 1';
    const { rows } = await pool.query(query, [bookName]);
    return rows.length > 0;
  } catch (error) {
    logger.error({ error, bookName }, 'Failed to validate book');
    return false;
  }
}

// Modified to return the message ID for confirmation
async function send(toJid: string, message: string): Promise<string | undefined> {
    // Show typing indicator
    await messageQueue.add('send-presence', { toJid, presence: 'composing' });
    
    // Calculate a natural delay based on message length
    const typingDelay = Math.min(2500, 800 + message.length * 12);
    const startTime = Date.now();
    
    // Ensure typing indicator is visible for a realistic time
    await ensureMinTypingDelay(startTime, typingDelay);
    
    // Send the message
    const job = await messageQueue.add('send-text', { toJid, text: message });
    
    // Stop typing indicator
    await messageQueue.add('send-presence', { toJid, presence: 'paused' });
    
    return job.id?.toString();
}

export class OnboardingHandler {
  public static async handle(user: User, message: string, userJid: string) {
    switch (user.onboarding_step) {
      case 'NEEDS_ONBOARDING':
        return this.handleNewUser(user, userJid);
      case 'AWAITING_AUTH':
        return this.handleAuthReply(user, message, userJid);
      case 'AWAITING_NAME':
        return this.handleNameReply(user, message, userJid);
      case 'AWAITING_REMINDER_TIME':
        return this.handleReminderTimeReply(user, message, userJid);
      case 'AWAITING_TIMEZONE':
        return this.handleTimezoneReply(user, message, userJid);
      case 'AWAITING_CONFESSION_CHOICE':
        return this.handleConfessionChoiceReply(user, message, userJid);
      case 'AWAITING_CONFESSION_INPUT':
        return this.handleConfessionInputDuringOnboarding(user, message, userJid);
      case 'AWAITING_JOURNEY':
        return this.handleJourneyReply(user, message, userJid);
    }
  }

  private static async handleNewUser(user: User, userJid: string) {
    // Guard against duplicate processing - if user is already in AWAITING_AUTH, don't send welcome again
    if (user.onboarding_step === 'AWAITING_AUTH') {
      logger.info({ userId: user.id }, 'User already in AWAITING_AUTH state, skipping duplicate welcome message');
      return;
    }

    // Prompt for access code first
    await send(
      userJid,
      "Welcome to Paro Bible Bot! To get started, please enter the access code to verify access."
    );

    // Move to auth-awaiting state
    await StateManager.updateOnboardingStep(user.id, 'AWAITING_AUTH');
  }

  // NEW: handle keyword verification
  private static async handleAuthReply(user: User, message: string, userJid: string) {
    if (containsAccessCode(message)) {
      await StateManager.markUserVerified(user.id);

      // Proceed to next onboarding question (name)
      await send(
        userJid,
        "Thank you! You're now verified. I'm Paro, your digital companion for a meaningful Bible journey. What's your first name?"
      );
      await StateManager.updateOnboardingStep(user.id, 'AWAITING_NAME');
    } else {
      await send(userJid, "Sorry, that's not the correct access code. Please try again.");
    }
  }
  
  private static async handleNameReply(user: User, receivedName: string, userJid: string) {
    const trimmed = receivedName.trim();
    const greetings = ['hi', 'hey', 'hello', 'yo', 'sup'];
    if (!trimmed || trimmed.length < 2 || greetings.includes(trimmed.toLowerCase())) {
      await send(userJid, "I'm sorry, I didn't catch your name. Could you tell me your first name? 😊");
      return;
    }
    const properName = trimmed.split(' ')[0];
    
    // Update the user's name
    await StateManager.updateUserName(user.id, properName);
    
    // Send the message but don't update state yet
    const messageId = await send(userJid, `It's truly wonderful to connect with you, ${properName}! I'd love to help you find a consistent rhythm with God's Word.\n\nTo set up your daily reminder, what time usually works best for you? (e.g., "8am" or "7:30 PM")`);
    
    // Only update the state after the message has been queued
    await StateManager.updateOnboardingStep(user.id, 'AWAITING_REMINDER_TIME');
  }
  
  private static async handleReminderTimeReply(user: User, message: string, userJid: string) {
    const parsedTime = chrono.parseDate(message);
    if (!parsedTime) {
      await send(userJid, "I couldn't understand that time. Please try again, for example: '7am', '8:15pm', or 'noon'.");
      return;
    }
    
    const timeString = parsedTime.toTimeString().split(' ')[0].substring(0, 5);
    await StateManager.updateUserReminder(user.id, timeString);
    
    // Send the message but don't update state yet
    const messageId = await send(userJid, `Perfect, ${user.name}! Your daily reminder is set for around ${timeString}. To make sure it reaches you at the right moment, could you share which country you're in? (e.g., Nigeria, UK, USA)`);
    
    // Only update the state after the message has been queued
    await StateManager.updateOnboardingStep(user.id, 'AWAITING_TIMEZONE');
  }
  
  private static async handleTimezoneReply(user: User, message: string, userJid: string) {
    const raw = message.trim().toLowerCase();
    const tzCandidate = countryToTz[raw] ?? message;
    try {
      new Intl.DateTimeFormat('en-US', { timeZone: tzCandidate }).format();
      await StateManager.updateUserTimezone(user.id, tzCandidate);
      
      // Schedule the first reminder based on the user's time preference and timezone
      if (user.reminder_time_pref) {
        const [hours, minutes] = user.reminder_time_pref.split(':').map(Number);
        
        // Create a DateTime object for today with the user's preferred time
        let reminderTime = DateTime.now()
          .setZone(tzCandidate)
          .set({ hour: hours, minute: minutes, second: 0, millisecond: 0 });
        
        // If the time has already passed today, schedule for tomorrow
        if (reminderTime < DateTime.now().setZone(tzCandidate)) {
          reminderTime = reminderTime.plus({ days: 1 });
        }
        
        // Convert to UTC for database storage
        const nextReminderAt = reminderTime.toUTC().toJSDate();
        await StateManager.scheduleNextReminder(user.id, nextReminderAt);
        logger.info({ userId: user.id, nextReminderAt }, 'Scheduled first reminder');
      }
      
      // Schedule the first check-in for tomorrow at a random time between 10AM-8PM
      await StateManager.scheduleInitialCheckIn(user.id);
      logger.info({ userId: user.id }, 'Scheduled first check-in');

      // After timezone setup, proceed directly to Bible book selection
      await this.proceedToJourneySelection(user, userJid);
    } catch (e) {
      await send(userJid, "Sorry, I couldn't recognise that location. Try 'Nigeria', 'UK', 'USA', or a time-zone such as 'Europe/London'.");
    }
  }

  private static async handleConfessionChoiceReply(user: User, message: string, userJid: string) {
    const choice = message.trim().toLowerCase();

    if (choice === 'yes' || choice === 'y') {
      // User wants to add a confession
      await send(userJid, `Okay, let's begin. What would you like to confess or reflect on today? You can type it out, and I'll help you refine it.`);

      // Update to confession input step instead of journey step
      await StateManager.updateOnboardingStep(user.id, 'AWAITING_CONFESSION_INPUT');

      // Start a confession session
      const { SessionService } = await import('./SessionService');
      await SessionService.startSession(user.id, {
        type: 'BIBLE_STUDY',
        step: 'CONFESSION_PENDING_INPUT',
        context: { isOnboarding: true }
      });

    } else if (choice === 'no' || choice === 'n') {
      // User wants to skip confession - complete onboarding since Bible book was already selected
      await send(userJid, `Perfect! You're all set up and ready to begin your Bible journey. Feel free to simply type 'start study' whenever you're ready to dive in, or ask me any questions about God's Word. I'm excited for what we'll discover together!`);
      await StateManager.updateOnboardingStep(user.id, 'ONBOARDING_COMPLETE');
    } else {
      // Invalid response
      await send(userJid, `Please type "yes" if you'd like to add a confession, or "no" to complete your setup.`);
    }
  }

  private static async handleConfessionInputDuringOnboarding(user: User, message: string, userJid: string) {
    // During onboarding, confession input should be handled by the active session
    // Find the active confession session and delegate to SessionHandlerV3
    const { SessionService } = await import('./SessionService');
    const { SessionHandlerV3 } = await import('./SessionHandlerV3');

    const activeSession = await SessionService.findActiveSession(user.id);

    // Check if there's an active confession session (any confession-related step)
    const confessionSteps = [
      'CONFESSION_PENDING_INPUT',
      'CONFESSION_AWAITING_CONFIRMATION',
      'CONFESSION_AWAITING_REMINDER_TIME',
      'CONFESSION_EDIT_PENDING_INPUT',
      'CONFESSION_EDIT_AWAITING_CONFIRMATION'
    ];

    if (activeSession && activeSession.session_step && confessionSteps.includes(activeSession.session_step)) {
      // Delegate to the session handler to process the confession
      return SessionHandlerV3.handle(activeSession, user, message);
    } else {
      // Fallback: no active confession session found
      await send(userJid, "I'm sorry, there seems to be an issue with your confession session. Let's continue with choosing your Bible book.");
      await this.proceedToJourneySelection(user, userJid);
    }
  }

  private static async proceedToJourneySelection(user: User, userJid: string) {
    // Simple prompt for any Bible book without listing options
    const bookMessage = `Now, let's choose where to begin our journey into God's Word together!\n\nWhich book of the Bible would you like to start with? (For example: John, Genesis, Psalms, Romans, etc.)`;

    // Send the message but don't update state yet
    await send(userJid, bookMessage);

    // Only update the state after the message has been queued
    await StateManager.updateOnboardingStep(user.id, 'AWAITING_JOURNEY');
  }

  private static async handleJourneyReply(user: User, message: string, userJid: string) {
    const choice = message.trim();

    // Validate the chosen book exists in database
    const isValid = await isValidBook(choice);
    if (!isValid) {
      await send(userJid, `I couldn't find "${choice}" in our Bible collection.\n\nPlease type the name of a Bible book (for example: John, Genesis, Psalms, Romans, etc.).`);
      return;
    }

    // Get the properly formatted book name from database
    const availableBooks = await getAvailableBooks();
    const chosenBook = availableBooks.find(book =>
      book.toLowerCase() === choice.toLowerCase()
    ) || choice;

    await StateManager.updateUserJourney(user.id, chosenBook);

    // After Bible book selection, introduce confession flow
    const confessionMessage = `Excellent choice, ${user.name}! You're all set for a wonderful journey in ${chosenBook}.\n\nBefore we begin, I'd love to offer you something special. Would you like to add a personal confession or reflection? I can help you refine it and set up a gentle reminder for you later. This is completely optional.\n\nType "yes" if you'd like to add a confession, or "no" to complete your setup and start studying.`;

    // Send the message but don't update state yet
    await send(userJid, confessionMessage);

    // Only update the state after the message has been queued
    await StateManager.updateOnboardingStep(user.id, 'AWAITING_CONFESSION_CHOICE');
  }
}