import { z } from 'zod';

// Define the schema for our environment variables
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.coerce.number().default(3000), // z.coerce.number() attempts to convert to a number
  DATABASE_URL: z.string().url(), // For direct database connection from Node.js
  REDIS_URL: z.string().url(),     // For direct Redis connection from Node.js
  EVOLUTION_API_URL: z.string().url(), // Our self-hosted Evolution API
  EVOLUTION_API_KEY: z.string().min(1), // API key to talk to Evolution API
  EVOLUTION_WEBHOOK_SECRET: z.string().min(1), // Secret for webhook validation
  GROQ_API_KEY: z.string().min(1), // For LLM and TTS
  VOYAGE_AI_API_KEY: z.string().min(1), // Add this line
  EVOLUTION_INSTANCE_NAME: z.string().min(1),
  CLOUDFLARE_ACCOUNT_ID: z.string().min(1), // Cloudflare account ID for R2
  R2_ACCESS_KEY_ID: z.string().min(1),
  R2_SECRET_ACCESS_KEY: z.string().min(1),
  R2_BUCKET_NAME: z.string().min(1),
  R2_PUBLIC_URL: z.string().url(),
});

// Validate and return the parsed environment variables
export function validateEnv() {

    console.log('Raw DATABASE_URL:', process.env.DATABASE_URL);
    console.log('Raw REDIS_URL:', process.env.REDIS_URL);
    console.log('!!! DEBUG: Raw EVOLUTION_API_URL is:', process.env.EVOLUTION_API_URL);
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('❌ Invalid environment variables:');
      for (const issue of error.issues) {
        console.error(`- ${issue.path.join('.')}: ${issue.message}`);
      }
      process.exit(1); // Exit the application if env vars are invalid
    }
    console.error('An unexpected error occurred during environment variable validation:', error);
    process.exit(1);
  }
}

// Export the inferred type for type safety throughout the application
export type Env = z.infer<typeof envSchema>;