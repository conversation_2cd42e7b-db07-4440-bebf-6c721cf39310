import 'dotenv/config';
import { pool } from '../database/client'; // Our shared database client
import { fullDbSchema } from '../database/schema'; // The CREATE TABLE statement
import * as fs from 'fs';
import * as path from 'path';

// Define the shape of a single Bible verse from our JSON file
interface BibleVerse {
  book: string;
  chapter: number;
  verse: number;
  text: string;
}

async function seed() {
  console.log('🌱 Starting database seeding...');
  const client = await pool.connect();

  try {
    console.log('Dropping existing rag_content table if it exists...');
    await client.query('DROP TABLE IF EXISTS rag_content CASCADE;');

    console.log('Creating tables and extensions...');
    await client.query(fullDbSchema);
    console.log('✅ Tables and extensions created successfully.');

    console.log('Reading Bible data from KJV JSON file...');
    // Correctly point to the file in the cloned repository outside our project folder
    const filePath = path.join(__dirname, '..', '..','..','..', 'bible-data-source', 'bibles', 'en-kjv', 'en-kjv.json');
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const rawData: Record<string, string> = JSON.parse(fileContent);

    console.log('Parsing raw data into structured format...');
    const bibleData: BibleVerse[] = [];
    for (const key in rawData) {
      const text = rawData[key];
      // Use regex to parse "Book Chapter:Verse"
      const match = key.match(/^(.*\s)(\d+):(\d+)$/);
      if (match) {
        const book = match[1].trim();
        const chapter = parseInt(match[2], 10);
        const verse = parseInt(match[3], 10);
        bibleData.push({ book, chapter, verse, text });
      }
    }
    console.log(`✅ Parsed ${bibleData.length} verses.`);

    console.log('Inserting verses into the database... (This may take a minute)');
    for (let i = 0; i < bibleData.length; i += 1000) {
      const batch = bibleData.slice(i, i + 1000);
      const values = batch.map(v => `('${v.book.replace(/'/g, "''")}', ${v.chapter}, ${v.verse}, '${v.text.replace(/'/g, "''")}', 'KJV')`).join(',');
      const query = `INSERT INTO rag_content (book, chapter, verse, text, translation) VALUES ${values};`;
      await client.query(query);
      console.log(`... Inserted batch ${Math.floor(i / 1000) + 1}`);
    }

    const { rows } = await client.query('SELECT COUNT(*) FROM rag_content;');
    console.log(`✅ Seeding complete! Inserted ${rows[0].count} verses into the database.`);

  } catch (error) {
    console.error('❌ An error occurred during database seeding:', error);
  } finally {
    console.log('Releasing database client.');
    client.release();
    await pool.end();
  }
}

seed();