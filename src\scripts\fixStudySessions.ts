// src/scripts/fixStudySessions.ts
// One-time script to fix the study_sessions table if it's missing the session_type column

import { config } from 'dotenv';
import { Pool } from 'pg';
import pino from 'pino';

// Use a simple logger for this script
const logger = pino({ transport: { target: 'pino-pretty' } });

// Load environment variables directly for this script
config();

async function fixStudySessions() {
  const dbUrl = process.env.DATABASE_URL;
  if (!dbUrl) {
    logger.error('❌ DATABASE_URL environment variable not set.');
    process.exit(1);
  }

  const pool = new Pool({ connectionString: dbUrl });
  const client = await pool.connect();
  
  logger.info('🔧 Checking study_sessions table structure...');

  try {
    // Check if study_sessions table exists but is missing session_type column
    const { rows } = await client.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables WHERE table_name = 'study_sessions'
      ) AS table_exists,
      EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'study_sessions' AND column_name = 'session_type'
      ) AS column_exists;
    `);
    
    const { table_exists, column_exists } = rows[0];
    
    if (table_exists && !column_exists) {
      logger.info('🔄 Found study_sessions table without session_type column. Recreating table...');
      
      // Begin transaction
      await client.query('BEGIN');
      
      // Backup existing data if needed (optional)
      await client.query('CREATE TEMP TABLE study_sessions_backup AS SELECT * FROM study_sessions');
      
      // Drop the existing table
      await client.query('DROP TABLE study_sessions CASCADE');
      
      // Create the types if they don't exist
      await client.query(`
        DO $$ 
        BEGIN 
          CREATE TYPE session_type AS ENUM ('BIBLE_STUDY', 'CHECK_IN', 'REMINDER'); 
        EXCEPTION WHEN duplicate_object THEN null; 
        END $$;
      `);
      
      await client.query(`
        DO $$ 
        BEGIN 
          CREATE TYPE session_status AS ENUM ('ACTIVE', 'PAUSED', 'COMPLETED', 'EXPIRED'); 
        EXCEPTION WHEN duplicate_object THEN
          ALTER TYPE session_status ADD VALUE IF NOT EXISTS 'EXPIRED';
        END $$;
      `);
      
      await client.query(`
        DO $$ 
        BEGIN
          CREATE TYPE study_step AS ENUM (
            'SENDING_SCENE',
            'SENDING_VERSES',
            'AWAITING_OBSERVATION',
            'AWAITING_INTERPRETATION',
            'AWAITING_APPLICATION',
            'SESSION_WRAP_UP',
            'NUDGE_SENT'
          );
        EXCEPTION WHEN duplicate_object THEN null;
        END $$;
      `);
      
      // Recreate the table with the correct structure
      await client.query(`
        CREATE TABLE study_sessions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          session_type session_type NOT NULL,
          status session_status NOT NULL DEFAULT 'ACTIVE',
          session_step study_step,
          session_context JSONB,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          expires_at TIMESTAMPTZ
        );
      `);
      
      // Recreate the trigger
      await client.query(`
        CREATE OR REPLACE FUNCTION touch_session_updated_at()
        RETURNS TRIGGER AS $$
        BEGIN
          NEW.updated_at := NOW();
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        
        CREATE TRIGGER trg_touch_session
        BEFORE UPDATE ON study_sessions
        FOR EACH ROW EXECUTE FUNCTION touch_session_updated_at();
      `);
      
      // Recreate the indexes
      await client.query(`
        CREATE INDEX idx_active_sessions
        ON study_sessions (user_id, status)
        WHERE status = 'ACTIVE';
        
        CREATE INDEX idx_stale_session_lookup
        ON study_sessions (status, updated_at)
        WHERE status = 'ACTIVE';
      `);
      
      await client.query('COMMIT');
      logger.info('✅ Successfully recreated study_sessions table with correct structure!');
    } else if (table_exists && column_exists) {
      logger.info('✅ study_sessions table already has the correct structure.');
    } else {
      logger.info('ℹ️ study_sessions table does not exist yet. It will be created by the regular migration script.');
    }
    
    client.release();
    await pool.end();
    process.exit(0);
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error({ err: error }, '❌ An error occurred during table fix. Transaction rolled back.');
    client.release();
    await pool.end();
    process.exit(1);
  }
}

fixStudySessions(); 