// src/utils/TimeUtils.ts
import { DateTime } from 'luxon';

/**
 * Computes a random time tomorrow between 10:00 and 20:00 **local** to the user
 * and returns it as a JavaScript Date in UTC (for storage in TIMESTAMPTZ).
 */
export function nextLocalCheckIn(userTz: string): Date {
  // Base = start of **tomorrow** in the user's zone
  const base = DateTime.now().setZone(userTz).plus({ days: 1 }).startOf('day');
  // Minutes after midnight representing 10:00 – 19:59 (inclusive)
  const offsetMinutes = 10 * 60 + Math.floor(Math.random() * (10 * 60));
  const localTime = base.plus({ minutes: offsetMinutes });
  // Convert to UTC for database storage
  return localTime.toUTC().toJSDate();
} 