// src/utils/messagePacer.ts
import { messageQueue } from '../queue/messageQueue';
import { logger } from '../config/logger.config';

interface Options {
  /** Delay in milliseconds between splitted message chunks. Default 1800ms */
  delayMs?: number;
  /** Whether to split the message by blank lines (\n\n) into multiple chat bubbles. Default true. */
  split?: boolean;
}

/**
 * Queues one or more `send-text` jobs so that the user receives messages in a paced, human–friendly manner.
 * – If `split === true` the message is broken on blank lines and sent as separate bubbles.
 * – Each subsequent bubble is delayed by `delayMs` milliseconds using BullMQ's built-in delay option.
 *
 * Errors are caught and logged; in case of failure a single unsplit message is queued immediately so the user still gets the content.
 */
export async function sendPaced(toJid: string, text: string, { delayMs = 1800, split = true }: Options = {}): Promise<void> {
  try {
    const chunks = split ? text.split(/\n{2,}/).filter(Boolean) : [text];

    for (let i = 0; i < chunks.length; i++) {
      await messageQueue.add(
        'send-text',
        { toJid, text: chunks[i].trim() },
        { delay: i === 0 ? 0 : delayMs }
      );
    }
  } catch (error) {
    logger.error({ error, toJid }, 'sendPaced failed – falling back to single message');
    await messageQueue.add('send-text', { toJid, text });
  }
}

// New helper: ensure a minimum gap between presence 'composing' and the actual reply.
// Usage: const started = Date.now(); // immediately after queuing send-presence('composing')
//        ... generate answer ...
//        await ensureMinTypingDelay(started, 1200); // waits only if Groq was too fast
export async function ensureMinTypingDelay(startedAt: number, minMs = 1200): Promise<void> {
  const elapsed = Date.now() - startedAt;
  if (elapsed < minMs) {
    await new Promise(resolve => setTimeout(resolve, minMs - elapsed));
  }
} 