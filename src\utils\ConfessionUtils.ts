// src/utils/ConfessionUtils.ts
import { DateTime } from 'luxon';
import * as chrono from 'chrono-node';

export class ConfessionUtils {
  /**
   * Parse reminder time input and return a DateTime object
   */
  public static parseReminderTime(timeInput: string, userTimezone: string = 'UTC'): DateTime | null {
    try {
      const parsedDate = chrono.parseDate(timeInput);
      if (!parsedDate) return null;

      let reminderTime = DateTime.fromJSDate(parsedDate).setZone(userTimezone);
      
      // If the time has already passed today, schedule for tomorrow
      if (reminderTime < DateTime.now().setZone(userTimezone)) {
        reminderTime = reminderTime.plus({ days: 1 });
      }
      
      return reminderTime;
    } catch (error) {
      return null;
    }
  }

  /**
   * Format confession reminder message
   */
  public static formatConfessionReminder(
    userName: string | null, 
    refinedText: string
  ): string {
    const name = userName || 'friend';
    return `Hello ${name}, it's time for your reflection:\n\n"${refinedText}"\n\nMay this serve as a gentle moment for prayer and growth.`;
  }

  /**
   * Validate confession text length and content
   */
  public static validateConfessionText(text: string): { isValid: boolean; message?: string } {
    const trimmed = text.trim();
    
    if (!trimmed) {
      return { 
        isValid: false, 
        message: "Please share something about what you'd like to confess or reflect on." 
      };
    }
    
    if (trimmed.length < 10) {
      return { 
        isValid: false, 
        message: "Please share a bit more about what you'd like to confess or reflect on. Take your time to express your thoughts." 
      };
    }
    
    if (trimmed.length > 2000) {
      return { 
        isValid: false, 
        message: "Your confession is quite long. Please try to keep it under 2000 characters for better processing." 
      };
    }
    
    return { isValid: true };
  }

  /**
   * Extract reminder frequency from user input
   */
  public static extractReminderFrequency(timeInput: string): string {
    const input = timeInput.toLowerCase();
    
    if (input.includes('every day') || input.includes('daily')) {
      return 'daily';
    }
    
    if (input.includes('every week') || input.includes('weekly')) {
      return 'weekly';
    }
    
    if (input.includes('every month') || input.includes('monthly')) {
      return 'monthly';
    }
    
    if (input.includes('every sunday') || input.includes('sundays')) {
      return 'weekly_sunday';
    }
    
    if (input.includes('every monday') || input.includes('mondays')) {
      return 'weekly_monday';
    }
    
    // Add more specific day patterns as needed
    
    // Default to 'once' for single reminders
    return 'once';
  }

  /**
   * Generate helpful time input examples
   */
  public static getTimeInputExamples(): string[] {
    return [
      'tomorrow at 7am',
      'every morning at 8:30am',
      'every Sunday at 10pm',
      'once at noon tomorrow',
      'daily at 6pm',
      'weekly on Friday at 9am'
    ];
  }

  /**
   * Check if confession text contains potentially sensitive content
   * This is a basic implementation - could be enhanced with more sophisticated filtering
   */
  public static checkContentSafety(text: string): { isSafe: boolean; concerns?: string[] } {
    const concerns: string[] = [];
    const lowerText = text.toLowerCase();
    
    // Check for extremely concerning content that might need professional help
    const crisisKeywords = [
      'kill myself', 'end my life', 'suicide', 'hurt myself', 
      'self harm', 'want to die', 'better off dead'
    ];
    
    const hasCrisisContent = crisisKeywords.some(keyword => lowerText.includes(keyword));
    
    if (hasCrisisContent) {
      concerns.push('crisis_content');
    }
    
    // Check for inappropriate content
    const inappropriateKeywords = [
      'explicit sexual content', 'graphic violence', 'illegal activities'
    ];
    
    // This is a simplified check - in production, you might want more sophisticated content filtering
    
    return {
      isSafe: concerns.length === 0,
      concerns: concerns.length > 0 ? concerns : undefined
    };
  }

  /**
   * Generate confession confirmation message
   */
  public static generateConfirmationMessage(
    userName: string | null,
    refinedText: string
  ): string {
    const name = userName || 'friend';
    return `Thank you for sharing, ${name}. I've refined it slightly to help you articulate it clearly. Does this capture what you wanted to express?\n\n"${refinedText}"\n\nType "yes" if this looks good, or "no" if you'd like to re-enter your confession.`;
  }

  /**
   * Generate edit confirmation message
   */
  public static generateEditConfirmationMessage(
    userName: string | null,
    refinedText: string
  ): string {
    const name = userName || 'friend';
    return `Thank you for sharing, ${name}. I've refined this new version. Does this capture what you wanted to express?\n\n"${refinedText}"\n\nType "yes" to save this updated version, or "no" to keep your original confession.`;
  }

  /**
   * Calculate next reminder time for recurring reminders
   */
  public static calculateNextReminder(
    frequency: string,
    lastReminderTime: DateTime,
    userTimezone: string = 'UTC'
  ): DateTime | null {
    const baseTime = lastReminderTime.setZone(userTimezone);
    
    switch (frequency) {
      case 'daily':
        return baseTime.plus({ days: 1 });
      
      case 'weekly':
        return baseTime.plus({ weeks: 1 });
      
      case 'monthly':
        return baseTime.plus({ months: 1 });
      
      case 'weekly_sunday':
        // Find next Sunday
        const daysUntilSunday = (7 - baseTime.weekday) % 7;
        return baseTime.plus({ days: daysUntilSunday === 0 ? 7 : daysUntilSunday });
      
      case 'weekly_monday':
        // Find next Monday
        const daysUntilMonday = (8 - baseTime.weekday) % 7;
        return baseTime.plus({ days: daysUntilMonday === 0 ? 7 : daysUntilMonday });
      
      // Add more specific day patterns as needed
      
      case 'once':
      default:
        return null; // No next reminder for one-time reminders
    }
  }

  /**
   * Format reminder frequency for user display
   */
  public static formatFrequencyDisplay(frequency: string): string {
    switch (frequency) {
      case 'daily':
        return 'every day';
      case 'weekly':
        return 'every week';
      case 'monthly':
        return 'every month';
      case 'weekly_sunday':
        return 'every Sunday';
      case 'weekly_monday':
        return 'every Monday';
      case 'once':
      default:
        return 'once';
    }
  }
}
