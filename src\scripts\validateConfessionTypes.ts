// src/scripts/validateConfessionTypes.ts
// Simple validation script for confession feature types and utilities

import pino from 'pino';
import { ConfessionUtils } from '../utils/ConfessionUtils';

const logger = pino({ transport: { target: 'pino-pretty' } });

async function validateConfessionTypes() {
  logger.info('🧪 Starting confession feature type validation...');
  
  try {
    // Test confession utilities
    await testConfessionUtils();
    
    logger.info('✅ All confession type validations passed!');
    
  } catch (error) {
    logger.error({ error }, '❌ Confession type validation failed');
    throw error;
  }
}

async function testConfessionUtils() {
  logger.info('Testing confession utilities...');
  
  // Test time parsing
  const timeInputs = [
    'tomorrow at 7am',
    'every morning at 8:30am',
    'once at noon',
    'invalid time input'
  ];
  
  for (const input of timeInputs) {
    const parsed = ConfessionUtils.parseReminderTime(input, 'UTC');
    logger.info({
      input,
      parsed: parsed?.toISO() || 'null'
    }, 'Time parsing result');
  }
  
  // Test text validation
  const textInputs = [
    '', // Empty
    'short', // Too short
    'This is a valid confession text that should pass validation',
    'x'.repeat(2500) // Too long
  ];
  
  for (const text of textInputs) {
    const validation = ConfessionUtils.validateConfessionText(text);
    logger.info({
      textLength: text.length,
      isValid: validation.isValid,
      message: validation.message
    }, 'Text validation result');
  }
  
  // Test frequency extraction
  const frequencyInputs = [
    'every day at 7am',
    'weekly on Sunday',
    'once tomorrow',
    'every month at noon'
  ];
  
  for (const input of frequencyInputs) {
    const frequency = ConfessionUtils.extractReminderFrequency(input);
    logger.info({
      input,
      frequency
    }, 'Frequency extraction result');
  }
  
  // Test message formatting
  const reminderMessage = ConfessionUtils.formatConfessionReminder(
    'John',
    'I need to work on my patience and understanding.'
  );
  
  logger.info({ reminderMessage }, 'Reminder message formatting test');
  
  // Test content safety check
  const safetyTests = [
    'I need to work on my patience',
    'I struggle with anger sometimes',
    'This is a normal confession text'
  ];
  
  for (const text of safetyTests) {
    const safety = ConfessionUtils.checkContentSafety(text);
    logger.info({
      text: text.substring(0, 50) + '...',
      isSafe: safety.isSafe,
      concerns: safety.concerns
    }, 'Content safety check');
  }
  
  // Test confirmation message generation
  const confirmationMessage = ConfessionUtils.generateConfirmationMessage(
    'John',
    'I need to work on my patience and show more understanding to others.'
  );
  
  logger.info({ confirmationMessage }, 'Confirmation message test');
  
  // Test edit confirmation message
  const editConfirmationMessage = ConfessionUtils.generateEditConfirmationMessage(
    'John',
    'I need to work on my patience and also practice forgiveness.'
  );
  
  logger.info({ editConfirmationMessage }, 'Edit confirmation message test');
  
  // Test frequency display formatting
  const frequencies = ['daily', 'weekly', 'monthly', 'once', 'weekly_sunday'];
  
  for (const freq of frequencies) {
    const display = ConfessionUtils.formatFrequencyDisplay(freq);
    logger.info({
      frequency: freq,
      display
    }, 'Frequency display formatting');
  }
  
  // Test time input examples
  const examples = ConfessionUtils.getTimeInputExamples();
  logger.info({ examples }, 'Time input examples');
  
  logger.info('Confession utilities test completed');
}

// Run the validation if this script is executed directly
if (require.main === module) {
  validateConfessionTypes()
    .then(() => {
      logger.info('Validation completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error({ error }, 'Validation failed');
      process.exit(1);
    });
}

export { validateConfessionTypes };
