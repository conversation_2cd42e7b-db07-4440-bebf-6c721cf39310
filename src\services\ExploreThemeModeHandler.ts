// src/services/ExploreThemeModeHandler.ts
// Explore a Theme mode implementation

import { logger } from '../config/logger.config';
import { StudySession, User, ExploreThemeContext } from '../types';
import { SessionService } from './SessionService';
import { StateManager } from './StateManager';
import { AIService } from './AIService';
import { MemoryService } from './MemoryService';
import { RAGService } from './RAGService';
import { messageQueue } from '../queue/messageQueue';
import { ensureMinTypingDelay } from '../utils/messagePacer';

async function send(toJid: string, message: string) {
  await messageQueue.add('send-presence', { toJid, presence: 'composing' });
  
  const typingDelay = Math.min(2500, 800 + message.length * 12);
  const startTime = Date.now();
  
  await ensureMinTypingDelay(startTime, typingDelay);
  await messageQueue.add('send-text', { toJid, text: message });
  await messageQueue.add('send-presence', { toJid, presence: 'paused' });
}

export class ExploreThemeModeHandler {
  /**
   * Handle Explore a Theme mode interactions
   */
  public static async handle(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ExploreThemeContext;
    const step = session.session_step;

    logger.info({
      sessionId: session.id,
      userId: user.id,
      step,
      theme: context?.theme
    }, 'Handling Explore Theme mode interaction');

    switch (step) {
      case 'EXPLORE_THEME_ACTIVE':
        if (!context.theme) {
          // Ask for theme
          await this.requestTheme(session, user, userJid);
        } else if (context.curatedVerses.length === 0) {
          // Search for verses on the theme
          await this.searchAndPresentVerses(session, user, message, userJid);
        } else {
          // Continue theme exploration discussion
          await this.handleThemeDiscussion(session, user, message, userJid);
        }
        break;

      case 'EXPLORE_THEME_WAITING':
        // User provided theme, now search for verses
        await this.handleThemeSelection(session, user, message, userJid);
        break;

      default:
        logger.warn({ sessionId: session.id, step }, 'Unknown Explore Theme step');
        await send(userJid, "Let's continue exploring this theme together.");
        break;
    }
  }

  /**
   * Request theme from user
   */
  private static async requestTheme(
    session: StudySession,
    user: User,
    userJid: string
  ): Promise<void> {
    const themePrompt = `What theme would you like to explore today? 🔍

Here are some ideas to get you started:
• **Love** - God's love, loving others
• **Faith** - Trust, belief, confidence in God
• **Hope** - Future promises, perseverance
• **Forgiveness** - God's mercy, forgiving others
• **Wisdom** - Making godly decisions
• **Peace** - Inner calm, God's peace
• **Joy** - True happiness, contentment
• **Prayer** - Talking with God, intercession

Or choose any other theme that's on your heart! Just tell me what you'd like to explore.`;

    await send(userJid, themePrompt);
    await SessionService.updateSessionStep(session.id, 'EXPLORE_THEME_WAITING');
  }

  /**
   * Handle user's theme selection
   */
  private static async handleThemeSelection(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const theme = message.trim();
    
    if (theme.length < 2) {
      await send(userJid, "Could you tell me a bit more about what theme you'd like to explore?");
      return;
    }

    await send(userJid, `Wonderful! Let's explore what the Bible says about "${theme}". 🙏`);
    await send(userJid, "I'm searching through Scripture to find the most relevant verses for us...");

    // Update context with theme
    const context = session.session_context as ExploreThemeContext;
    const updatedContext: ExploreThemeContext = {
      ...context,
      theme,
      curatedVerses: [],
      themeInsights: []
    };

    await SessionService.updateSessionContext(session.id, updatedContext);
    await SessionService.updateSessionStep(session.id, 'EXPLORE_THEME_ACTIVE');

    // Search for verses
    await this.searchAndPresentVerses(session, user, theme, userJid);
  }

  /**
   * Search for verses and present them
   */
  private static async searchAndPresentVerses(
    session: StudySession,
    user: User,
    theme: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ExploreThemeContext;

    try {
      // Search for relevant verses using RAG
      const searchResults = await RAGService.search(theme, 8); // Get more to filter

      if (searchResults.length === 0) {
        await send(userJid, `I couldn't find specific verses about "${theme}" right now.\n\nWould you like to try a different theme or explore something else?`);
        await this.requestTheme(session, user, userJid);
        return;
      }

      // Filter and curate the best verses (3-5 from different books)
      const curatedVerses = this.curateVerses(searchResults);

      if (curatedVerses.length === 0) {
        await send(userJid, `I found some verses, but they might not be the best match. Would you like to try rephrasing your theme?`);
        await this.requestTheme(session, user, userJid);
        return;
      }

      // Present the verses in a bundled format
      await this.presentCuratedVerses(session, user, curatedVerses, userJid);

    } catch (error) {
      logger.error({ error, sessionId: session.id, theme }, 'Failed to search for theme verses');
      await send(userJid, "I'm having trouble searching right now. Let me try again in a moment.");
    }
  }

  /**
   * Curate verses to get 3-5 from different books with good relevance
   */
  private static curateVerses(searchResults: any[]): Array<{
    book: string;
    chapter: number;
    verse: number;
    text: string;
    relevanceScore: number;
  }> {
    // Filter by relevance (distance < 0.3 means good match)
    const relevantVerses = searchResults.filter(result => result.distance < 0.35);
    
    if (relevantVerses.length === 0) {
      return [];
    }

    // Group by book to ensure diversity
    const versesByBook = new Map<string, any[]>();
    relevantVerses.forEach(verse => {
      if (!versesByBook.has(verse.book)) {
        versesByBook.set(verse.book, []);
      }
      versesByBook.get(verse.book)!.push(verse);
    });

    // Select best verse from each book, up to 5 books
    const curatedVerses: any[] = [];
    const bookNames = Array.from(versesByBook.keys()).slice(0, 5);

    bookNames.forEach(book => {
      const bookVerses = versesByBook.get(book)!;
      // Take the most relevant verse from this book
      const bestVerse = bookVerses.sort((a, b) => a.distance - b.distance)[0];
      curatedVerses.push({
        book: bestVerse.book,
        chapter: bestVerse.chapter,
        verse: bestVerse.verse,
        text: bestVerse.text,
        relevanceScore: 1 - bestVerse.distance // Convert distance to relevance score
      });
    });

    // Ensure we have at least 3 verses, if not, add more from top results
    while (curatedVerses.length < 3 && curatedVerses.length < relevantVerses.length) {
      const nextBest = relevantVerses.find(v => 
        !curatedVerses.some(cv => cv.book === v.book && cv.chapter === v.chapter && cv.verse === v.verse)
      );
      if (nextBest) {
        curatedVerses.push({
          book: nextBest.book,
          chapter: nextBest.chapter,
          verse: nextBest.verse,
          text: nextBest.text,
          relevanceScore: 1 - nextBest.distance
        });
      } else {
        break;
      }
    }

    return curatedVerses.slice(0, 5); // Max 5 verses
  }

  /**
   * Present curated verses in bundled format
   */
  private static async presentCuratedVerses(
    session: StudySession,
    user: User,
    curatedVerses: any[],
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ExploreThemeContext;
    const theme = context.theme;

    // Format verses for presentation
    const versesText = curatedVerses
      .map(v => `**${v.book} ${v.chapter}:${v.verse}**\n${v.text}`)
      .join('\n\n');

    const presentationMessage = `Here's what I found about **${theme}** across Scripture:\n\n${versesText}`;

    await send(userJid, presentationMessage);

    // Generate synthesizing question
    const synthesizingQuestion = await AIService.generateThemeSynthesisQuestion(theme, curatedVerses);
    await send(userJid, synthesizingQuestion);

    // Update context
    const updatedContext: ExploreThemeContext = {
      ...context,
      curatedVerses,
      themeInsights: []
    };

    await SessionService.updateSessionContext(session.id, updatedContext);

    // Store theme exploration in memory
    await StateManager.updateLastThemeExplored(user.id, theme);

    logger.info({
      sessionId: session.id,
      userId: user.id,
      theme,
      versesCount: curatedVerses.length,
      books: curatedVerses.map(v => v.book)
    }, 'Presented curated verses for theme exploration');
  }

  /**
   * Handle ongoing theme discussion
   */
  private static async handleThemeDiscussion(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ExploreThemeContext;

    // Store user's insight
    await MemoryService.addThemeExploration(
      user.id,
      context.theme,
      context.curatedVerses,
      message
    );

    // Generate response that builds on their insight
    const aiResponse = await AIService.generateThemeExplorationResponse({
      theme: context.theme,
      userInsight: message,
      verses: context.curatedVerses,
      previousInsights: context.themeInsights
    });

    await send(userJid, aiResponse);

    // Update context with new insight
    const updatedContext: ExploreThemeContext = {
      ...context,
      themeInsights: [...context.themeInsights, message]
    };

    await SessionService.updateSessionContext(session.id, updatedContext);

    // Check if we've had enough exploration (3-4 insights) and automatically progress
    if (updatedContext.themeInsights.length >= 3) {
      await send(userJid, "What a rich exploration we've had! Let's discover a new theme to dive into... 🌟");
      await this.requestTheme(session, user, userJid);
    } else {
      // Continue current discussion automatically
      await send(userJid, "What else do you notice about this theme in these verses?");
    }
  }



  /**
   * End theme exploration session
   */
  private static async endThemeExplorationSession(
    session: StudySession,
    user: User,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ExploreThemeContext;

    const summary = await AIService.generateThemeExplorationSummary(
      context.theme,
      context.curatedVerses,
      context.themeInsights
    );

    await send(userJid, summary);
    await SessionService.endSession(session.id, 'COMPLETED');

    logger.info({
      sessionId: session.id,
      userId: user.id,
      theme: context.theme,
      insightsCount: context.themeInsights.length
    }, 'Completed theme exploration session');
  }
}
