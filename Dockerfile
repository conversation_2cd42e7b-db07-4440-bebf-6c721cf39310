# Stage 1: The Builder
# We use a full Node.js image to install all dependencies and build our TypeScript code.
FROM node:20-alpine AS builder
WORKDIR /app

# Copy package files and install ALL dependencies (including devDependencies)
COPY package*.json ./
RUN npm install

# Copy the rest of our source code
COPY . .

# Compile TypeScript to JavaScript. The output will be in the /app/dist folder.
RUN npm run build # We will add this 'build' script to package.json

# Stage 2: The Production Image
# We start from a fresh, lightweight Node.js image.
FROM node:20-alpine AS production

# Set the working directory
WORKDIR /app

# Copy only the necessary production dependencies from the 'builder' stage
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# Copy the compiled JavaScript output from the 'builder' stage
COPY --from=builder /app/dist ./dist

# This is the command that will be run when the container starts
CMD ["sh", "-c", "npm run db:migrate:prod && npm run start"]