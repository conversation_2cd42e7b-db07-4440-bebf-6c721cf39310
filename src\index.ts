import 'dotenv/config';                     // ❶ load .env first, and only once
import { StateManager } from './services/StateManager';
import express from 'express';
import { validateEnv } from './config/env.config';
import { handleIncomingMessage } from './services/MessageHandler';
import { testDbConnection } from './database/client';
import { logger } from './config/logger.config';
import { messageWorker } from './queue/messageWorker'; // Import the worker
import { SchedulerService } from './services/SchedulerService'; // Import scheduler

// ---------------------------------------------------------------------------
// Runtime checks
// ---------------------------------------------------------------------------
const env = validateEnv();                 // throws if anything is missing

if (env.NODE_ENV !== 'production') {       // optional debug block
  console.log('Loaded environment:', {
    DATABASE_URL: env.DATABASE_URL,
    REDIS_URL:    env.REDIS_URL,
    // add more if you want to inspect them
  });
}

logger.info('Paro Bible Bot Backend is starting...');
// ---------------------------------------------------------------------------
// HTTP server
// ---------------------------------------------------------------------------
const app  = express();
const PORT = env.PORT || 3000; // Use port from env, or default to 3000

app.use(express.json()); // Middleware to parse JSON request bodies

// Basic health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({ status: 'ok', message: 'Paro Bible Bot Backend is running!' });
});

// Test DB connection and start the server
testDbConnection().then(() => {
  app.listen(PORT, () => {
    logger.info(`Server is listening on port ${PORT}`);
    // Log that the worker is running
    logger.info(`Message worker is running and connected to Redis: ${messageWorker.isRunning()}`);
    // SchedulerService now handles both reminders and check-ins.
    SchedulerService.start();
  });
});

// The main webhook endpoint for all incoming WhatsApp events
app.post('/webhook', (req, res) => {
  // Immediately acknowledge the webhook to prevent timeouts
  res.status(200).send('Webhook received.');
  
  // Process the message in the background.
  // We don't use `await` here so that the response is sent immediately.
  handleIncomingMessage(req.body).catch(error => {
    console.error('Unhandled error in message handler:', error);
  });
});

