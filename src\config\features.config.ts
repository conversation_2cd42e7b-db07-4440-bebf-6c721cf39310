// src/config/features.config.ts
// Feature flags for controlling new functionality rollout

export interface FeatureFlags {
  // Session Mode Lock-in System
  enableSessionModeLockIn: boolean;
  
  // Individual mode flags for granular control
  enableDeepDiveMode: boolean;
  enableChapterOverviewMode: boolean;
  enableExploreThemeMode: boolean;
  
  // Memory and welcome system
  enableIntelligentWelcome: boolean;
  enableEnhancedMemory: boolean;
  
  // Dynamic chunking for chapter overview
  enableDynamicChunking: boolean;
  
  // Advanced system prompts
  enableAdvancedPrompts: boolean;
}

/**
 * Get current feature flag configuration from environment variables
 */
export function getFeatureFlags(): FeatureFlags {
  return {
    // Main session mode system - ENABLED by default as requested
    enableSessionModeLockIn: process.env.ENABLE_SESSION_MODE_LOCK_IN !== 'false',
    
    // Individual modes - all enabled by default
    enableDeepDiveMode: process.env.ENABLE_DEEP_DIVE_MODE !== 'false',
    enableChapterOverviewMode: process.env.ENABLE_CHAPTER_OVERVIEW_MODE !== 'false',
    enableExploreThemeMode: process.env.ENABLE_EXPLORE_THEME_MODE !== 'false',
    
    // Memory features - enabled by default
    enableIntelligentWelcome: process.env.ENABLE_INTELLIGENT_WELCOME !== 'false',
    enableEnhancedMemory: process.env.ENABLE_ENHANCED_MEMORY !== 'false',
    
    // Dynamic chunking - enabled by default
    enableDynamicChunking: process.env.ENABLE_DYNAMIC_CHUNKING !== 'false',
    
    // Advanced prompts - enabled by default
    enableAdvancedPrompts: process.env.ENABLE_ADVANCED_PROMPTS !== 'false',
  };
}

/**
 * Check if the new session mode system should be used
 */
export function shouldUseSessionModeLockIn(): boolean {
  return getFeatureFlags().enableSessionModeLockIn;
}

/**
 * Check if a specific study mode is enabled
 */
export function isStudyModeEnabled(mode: 'DEEP_DIVE' | 'CHAPTER_OVERVIEW' | 'EXPLORE_THEME'): boolean {
  const flags = getFeatureFlags();
  
  switch (mode) {
    case 'DEEP_DIVE':
      return flags.enableDeepDiveMode;
    case 'CHAPTER_OVERVIEW':
      return flags.enableChapterOverviewMode;
    case 'EXPLORE_THEME':
      return flags.enableExploreThemeMode;
    default:
      return false;
  }
}

/**
 * Get available study modes based on feature flags
 */
export function getAvailableStudyModes(): Array<'DEEP_DIVE' | 'CHAPTER_OVERVIEW' | 'EXPLORE_THEME'> {
  const modes: Array<'DEEP_DIVE' | 'CHAPTER_OVERVIEW' | 'EXPLORE_THEME'> = [];
  
  if (isStudyModeEnabled('DEEP_DIVE')) {
    modes.push('DEEP_DIVE');
  }
  if (isStudyModeEnabled('CHAPTER_OVERVIEW')) {
    modes.push('CHAPTER_OVERVIEW');
  }
  if (isStudyModeEnabled('EXPLORE_THEME')) {
    modes.push('EXPLORE_THEME');
  }
  
  return modes;
}
