// src/services/WelcomeService.ts
// Intelligent welcome system with memory integration

import { logger } from '../config/logger.config';
import { User } from '../types';
import { MemoryService } from './MemoryService';
import { StateManager } from './StateManager';
import { getFeatureFlags } from '../config/features.config';

export class WelcomeService {
  /**
   * Determine if user is new or returning for the current book
   */
  public static async isNewUserForBook(user: User): Promise<boolean> {
    const currentBook = user.current_book || 'John';
    
    // Check if user has any progress in current book
    const hasProgress = (user.last_completed_chapter || 0) > 0 || (user.last_completed_verse || 0) > 0;
    
    // Check if user has any insights for current book
    const insights = await MemoryService.getUserBibleInsights(user.id, currentBook);
    
    return !hasProgress && insights.length === 0;
  }

  /**
   * Generate personalized welcome message based on user history
   */
  public static async generatePersonalizedWelcome(user: User): Promise<string> {
    const flags = getFeatureFlags();
    
    if (!flags.enableIntelligentWelcome) {
      return this.generateBasicWelcome(user);
    }

    const userName = user.name || 'friend';
    const currentBook = user.current_book || 'John';
    const isNewUser = await this.isNewUserForBook(user);

    if (isNewUser) {
      return this.generateNewUserWelcome(userName, currentBook);
    } else {
      return this.generateReturningUserWelcome(user, userName, currentBook);
    }
  }

  /**
   * Generate welcome for first-time users of a book
   */
  private static generateNewUserWelcome(userName: string, book: string): string {
    const bookIntros: Record<string, string> = {
      'John': `Welcome to the Gospel of John, ${userName}! This beautiful book reveals Jesus as the Word made flesh, full of grace and truth. You're about to discover some of the most profound truths about who Jesus is and what He came to do.`,
      'Proverbs': `Welcome to Proverbs, ${userName}! This treasure trove of wisdom will guide you in making godly decisions and living with understanding. Each verse is like a gem waiting to be discovered.`,
      'Psalms': `Welcome to the Psalms, ${userName}! These ancient songs and prayers will speak to every emotion and season of your heart. David and other psalmists pour out their souls before God, showing us how to do the same.`,
      'Romans': `Welcome to Romans, ${userName}! Paul's masterpiece will take you on a journey through the gospel - from our need for salvation to the glorious life we have in Christ. Prepare for some life-changing truths!`,
      'Genesis': `Welcome to Genesis, ${userName}! You're about to explore the very beginning - how God created everything, chose a people, and began His plan of redemption. Every story here points forward to Jesus.`
    };

    return bookIntros[book] || `Welcome to the book of ${book}, ${userName}! You're about to embark on a wonderful journey of discovery in God's Word. I'm excited to explore these truths with you.`;
  }

  /**
   * Generate welcome for returning users with memory integration
   */
  private static async generateReturningUserWelcome(user: User, userName: string, book: string): Promise<string> {
    // Get recent insight for personalization
    const recentInsight = await MemoryService.getRecentInsightForWelcome(user.id, book);
    
    // Get user's study preferences
    const preferences = await StateManager.getUserStudyPreferences(user.id);
    
    let welcomeMessage = `Welcome back, ${userName}! It's wonderful to continue our journey through ${book} together.`;

    // Add insight reference if available
    if (recentInsight) {
      const passageRef = this.extractPassageReference(recentInsight.passage);
      const insightSummary = this.summarizeInsight(recentInsight.response);
      
      welcomeMessage += ` Last time, we explored ${passageRef}, and I was struck by your insight about ${insightSummary}.`;
    }

    // Add progress encouragement
    const progress = this.calculateProgress(user, book);
    if (progress.chaptersCompleted > 0) {
      welcomeMessage += ` You've made great progress - ${progress.chaptersCompleted} chapters completed! `;
    }

    // Add study mode suggestion based on history
    if (preferences.lastStudyModeUsed && preferences.totalStudySessions > 2) {
      const modeNames: Record<string, string> = {
        'DEEP_DIVE': 'Deep Dive',
        'CHAPTER_OVERVIEW': 'Chapter Overview',
        'EXPLORE_THEME': 'Explore a Theme'
      };
      const modeName = modeNames[preferences.lastStudyModeUsed] || preferences.lastStudyModeUsed;
      welcomeMessage += ` I noticed you really enjoyed ${modeName} mode last time.`;
    }

    welcomeMessage += ` I'm excited to see what God will reveal to us today.`;

    return welcomeMessage;
  }

  /**
   * Generate basic welcome (fallback)
   */
  private static generateBasicWelcome(user: User): string {
    const userName = user.name || 'friend';
    const currentBook = user.current_book || 'John';
    
    return `Welcome back, ${userName}! Ready to dive into ${currentBook} together?`;
  }

  /**
   * Extract clean passage reference from full passage string
   */
  private static extractPassageReference(passage: string): string {
    // Remove book name and clean up the reference
    const parts = passage.split(' ');
    if (parts.length > 1) {
      return parts.slice(1).join(' ');
    }
    return passage;
  }

  /**
   * Summarize user insight for welcome message
   */
  private static summarizeInsight(insight: string): string {
    // Clean and truncate insight for welcome message
    let summary = insight.trim();
    
    // Remove common filler words and phrases
    summary = summary.replace(/^(well,|so,|i think|i believe|i feel|it seems)/i, '');
    summary = summary.trim();
    
    // Take first meaningful sentence or phrase
    const sentences = summary.split(/[.!?]+/);
    let firstSentence = sentences[0].trim();
    
    // If too long, take first meaningful phrase
    if (firstSentence.length > 60) {
      const words = firstSentence.split(' ');
      firstSentence = words.slice(0, 10).join(' ');
      if (words.length > 10) {
        firstSentence += '...';
      }
    }
    
    return firstSentence || 'your thoughtful reflection';
  }

  /**
   * Calculate user's progress in current book
   */
  private static calculateProgress(user: User, book: string): {
    chaptersCompleted: number;
    versesCompleted: number;
    progressPercentage: number;
  } {
    const chaptersCompleted = user.last_completed_chapter || 0;
    const versesCompleted = user.last_completed_verse || 0;
    
    // Rough chapter counts for major books (could be made more accurate)
    const bookChapterCounts: Record<string, number> = {
      'John': 21,
      'Proverbs': 31,
      'Psalms': 150,
      'Romans': 16,
      'Genesis': 50,
      'Matthew': 28,
      'Mark': 16,
      'Luke': 24
    };

    const totalChapters = bookChapterCounts[book] || 25; // Default estimate
    const progressPercentage = Math.round((chaptersCompleted / totalChapters) * 100);
    
    return {
      chaptersCompleted,
      versesCompleted,
      progressPercentage: Math.min(progressPercentage, 100)
    };
  }

  /**
   * Generate study mode suggestion based on user history and context
   */
  public static async generateModeRecommendation(user: User): Promise<string | null> {
    const preferences = await StateManager.getUserStudyPreferences(user.id);
    const flags = getFeatureFlags();
    
    if (!flags.enableIntelligentWelcome) {
      return null;
    }

    // If user has a clear preference, suggest it
    if (preferences.preferredStudyMode && preferences.totalStudySessions > 3) {
      const modeNames: Record<string, string> = {
        'DEEP_DIVE': 'Deep Dive',
        'CHAPTER_OVERVIEW': 'Chapter Overview',
        'EXPLORE_THEME': 'Explore a Theme'
      };
      const modeName = modeNames[preferences.preferredStudyMode];
      return `Based on your previous sessions, you might enjoy ${modeName} mode today.`;
    }

    // If user recently explored a theme, suggest continuing
    if (preferences.lastThemeExplored) {
      return `I noticed you recently explored "${preferences.lastThemeExplored}". Would you like to dive deeper into that theme or explore something new?`;
    }

    // For new users, suggest starting with Deep Dive
    if (preferences.totalStudySessions === 0) {
      return `Since this is your first session, I'd recommend starting with Deep Dive mode to really connect with the text.`;
    }

    return null;
  }
}
