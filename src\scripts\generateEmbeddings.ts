import 'dotenv/config';
import { pool } from '../database/client';
import { validateEnv } from '../config/env.config';
import { VoyageAIClient, VoyageAIError } from 'voyageai';
import pgvector from 'pgvector/pg';

const env = validateEnv();

// Ensure the API key is present before initializing
if (!env.VOYAGE_AI_API_KEY) {
  console.error('❌ Voyage AI API key is missing in your .env file.');
  process.exit(1);
}

const vo = new VoyageAIClient({ apiKey: env.VOYAGE_AI_API_KEY });

interface VerseForEmbedding {
  id: number;
  text: string;
}

async function generate() {
  console.log('🚀 Starting vector embedding generation with voyage-3-large...');
  const client = await pool.connect();

  try {
    // First, ensure the `embedding` column is of the correct size for voyage-3-large (1024)
    console.log('Verifying database schema for vector(1024)...');
    // This is a safe way to alter the table only if needed.
    // It won't fail if the column is already the correct type.
    await client.query('ALTER TABLE rag_content ALTER COLUMN embedding TYPE vector(1024);');
    console.log('✅ Database schema verified.');

    console.log('Fetching all verses from the database that need embedding...');
    const { rows: verses }: { rows: VerseForEmbedding[] } = await client.query(
      'SELECT id, text FROM rag_content WHERE embedding IS NULL;'
    );

    if (verses.length === 0) {
      console.log('✅ No new verses to embed. Database is up to date.');
      return;
    }

    console.log(`Found ${verses.length} verses to embed.`);
    console.log('Generating embeddings in batches... (This will take a very long time!)');

    // Voyage AI can handle up to 128 texts per batch for most models
    for (let i = 0; i < verses.length; i += 128) {
      const batch = verses.slice(i, i + 128);
      const texts = batch.map(v => v.text);

      const result = await vo.embed({
        input: texts,
        model: 'voyage-3-large',
      }).catch((err: unknown) => {
        console.error('Raw error from Voyage AI:', JSON.stringify(err, null, 2));
        throw err; // Re-throw to be caught by the outer catch block
      });

      // A small delay to respect API rate limits if any
      await new Promise(resolve => setTimeout(resolve, 1000)); // 1-second delay

      if (result.data) {
        // Use a transaction for updating a batch for safety
        await client.query('BEGIN');
        for (let j = 0; j < result.data.length; j++) {
          const verseId = batch[j].id;
          const embedding = result.data[j].embedding;
          await client.query(
            'UPDATE rag_content SET embedding = $1 WHERE id = $2',
            [pgvector.toSql(embedding), verseId]
          );
        }
        await client.query('COMMIT');
      }
      console.log(`... Processed batch ${Math.floor(i / 128) + 1} of ${Math.ceil(verses.length / 128)}`);
    }

    console.log('✅ Embedding generation complete!');

  } catch (error) {
    // Make sure to roll back any open transaction
    try {
      await client.query('ROLLBACK');
    } catch (rollbackError) {
      console.error('Error during rollback:', rollbackError);
    }
    
    // Use the proper VoyageAIError handling as per documentation
    if (error instanceof VoyageAIError) {
      console.error('❌ Voyage AI Error:', {
        statusCode: error.statusCode,
        message: error.message,
        body: error.body
      });
    } else if (error && typeof error === 'object' && 'message' in error) {
      // Handle other types of errors
      console.error('❌ Error during embedding generation:', error.message);
    } else {
      // Unknown error structure
      console.error('❌ Unknown error during embedding generation:', error);
    }
  } finally {
    console.log('Releasing database client.');
    client.release();
    await pool.end();
  }
}

generate();