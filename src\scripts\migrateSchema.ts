// src/scripts/migrateSchema.ts
// This is a standalone, robust, and transactional migration script.

import { config } from 'dotenv';
import { Pool } from 'pg';
import { fullDbSchema } from '../database/schema';
import pino from 'pino';

// Use a simple logger for this script, separate from the main app logger
const logger = pino({ transport: { target: 'pino-pretty' } });

// Load environment variables directly for this script
config();

async function migrate() {
  const dbUrl = process.env.DATABASE_URL;
  if (!dbUrl) {
    logger.error('❌ DATABASE_URL environment variable not set.');
    process.exit(1); // Exit with failure code
  }

  const pool = new Pool({ connectionString: dbUrl });
  const client = await pool.connect();
  
  logger.info('🚀 Applying database schema migrations...');

  try {
    // Wrap the entire schema application in a single transaction
    await client.query('BEGIN');
    await client.query(fullDbSchema);
    await client.query('COMMIT');
    
    logger.info('✅ Schema migration applied successfully!');
    client.release();
    await pool.end();
    process.exit(0); // Exit with success code
  } catch (error) {
    // If any part fails, roll back the entire transaction
    await client.query('ROLLBACK');
    logger.error({ err: error }, '❌ An error occurred during schema migration. Transaction rolled back.');
    client.release();
    await pool.end();
    process.exit(1); // Exit with failure code
  }
}

migrate();