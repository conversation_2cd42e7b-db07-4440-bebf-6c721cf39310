// src/services/SessionHandlerV3.ts
// New Session Mode Lock-in Handler
import { logger } from '../config/logger.config';
import { messageQueue } from '../queue/messageQueue';
import { StudySession, User, StudyMode } from '../types';
import { SessionService } from './SessionService';
import { SessionHandler } from './SessionHandler';
import { SessionModeService } from './SessionModeService';
import { ensureMinTypingDelay } from '../utils/messagePacer';
import { shouldUseSessionModeLockIn } from '../config/features.config';
import { DeepDiveModeHandler } from './DeepDiveModeHandler';
import { ChapterOverviewModeHandler } from './ChapterOverviewModeHandler';
import { ExploreThemeModeHandler } from './ExploreThemeModeHandler';
import { ConfessionHandler } from './ConfessionHandler';

async function send(toJid: string, message: string) {
  await messageQueue.add('send-presence', { toJid, presence: 'composing' });
  
  const typingDelay = Math.min(2500, 800 + message.length * 12);
  const startTime = Date.now();
  
  await ensureMinTypingDelay(startTime, typingDelay);
  await messageQueue.add('send-text', { toJid, text: message });
  await messageQueue.add('send-presence', { toJid, presence: 'paused' });
}

/**
 * V3 Session Handler with Session Mode Lock-in system
 * Falls back to original SessionHandler for non-Bible study sessions or when feature is disabled
 */
export class SessionHandlerV3 {
  /**
   * Main entry point - determines whether to use new or old system
   */
  public static async handle(session: StudySession, user: User, message: string) {
    const userJid = user.jid!;
    
    // For non-Bible study sessions, always use original handler
    if (session.session_type !== 'BIBLE_STUDY') {
      return SessionHandler.handle(session, user, message);
    }
    
    // Check if new session mode system is enabled
    if (!shouldUseSessionModeLockIn()) {
      return SessionHandler.handle(session, user, message);
    }
    
    // Use new session mode lock-in system
    return this.handleBibleStudyMessage(session, user, message, userJid);
  }

  /**
   * Handle Bible study messages with Session Mode Lock-in
   */
  private static async handleBibleStudyMessage(
    session: StudySession, 
    user: User, 
    message: string, 
    userJid: string
  ): Promise<void> {
    const step = session.session_step || 'SENDING_SCENE';
    const context = session.session_context || {};
    
    logger.info({ 
      sessionId: session.id, 
      userId: user.id, 
      step, 
      studyMode: session.study_mode 
    }, 'Handling Bible study message with Session Mode Lock-in');

    switch (step) {
      case 'SENDING_SCENE':
        // If no study mode is set, we need to start the mode selection process
        if (!session.study_mode) {
          await this.initiateSessionModeSelection(session, user, userJid);
        } else {
          // Mode already selected, delegate to mode-specific handler
          await this.delegateToModeHandler(session, user, message, userJid);
        }
        break;

      case 'AWAITING_MODE_SELECTION':
        await this.handleModeSelection(session, user, message, userJid);
        break;

      case 'DEEP_DIVE_ACTIVE':
        await this.handleDeepDiveMode(session, user, message, userJid);
        break;

      case 'CHAPTER_OVERVIEW_ACTIVE':
      case 'CHAPTER_OVERVIEW_WAITING':
        await this.handleChapterOverviewMode(session, user, message, userJid);
        break;

      case 'EXPLORE_THEME_ACTIVE':
      case 'EXPLORE_THEME_WAITING':
        await this.handleExploreThemeMode(session, user, message, userJid);
        break;

      case 'CONFESSION_PENDING_INPUT':
      case 'CONFESSION_AWAITING_CONFIRMATION':
      case 'CONFESSION_AWAITING_REMINDER_TIME':
      case 'CONFESSION_EDIT_PENDING_INPUT':
      case 'CONFESSION_EDIT_AWAITING_CONFIRMATION':
        await this.handleConfessionMode(session, user, message, userJid);
        break;

      default:
        // For any other steps, fall back to original handler
        logger.info({ 
          sessionId: session.id, 
          step 
        }, 'Falling back to original handler for unrecognized step');
        await SessionHandler.handle(session, user, message);
        break;
    }
  }

  /**
   * Initiate the session mode selection process
   */
  private static async initiateSessionModeSelection(
    session: StudySession, 
    user: User, 
    userJid: string
  ): Promise<void> {
    // Generate welcome message and mode selection menu
    const welcomeMessage = await SessionModeService.generateWelcomeBackMessage(user);
    await send(userJid, welcomeMessage);

    const modeMenu = SessionModeService.generateModeSelectionMenu();
    await send(userJid, modeMenu);

    // Update session to await mode selection
    await SessionService.updateSessionStep(session.id, 'AWAITING_MODE_SELECTION');
  }

  /**
   * Handle user's study mode selection
   */
  private static async handleModeSelection(
    session: StudySession, 
    user: User, 
    message: string, 
    userJid: string
  ): Promise<void> {
    const selectedMode = SessionModeService.parseStudyModeChoice(message);
    
    if (!selectedMode) {
      await send(userJid, "I didn't catch that. Please choose a number from the options above.");
      return;
    }

    // Confirm selection and initialize the chosen mode
    const modeNames = {
      'DEEP_DIVE': 'Deep Dive',
      'CHAPTER_OVERVIEW': 'Chapter Overview',
      'EXPLORE_THEME': 'Explore a Theme'
    };

    await send(userJid, `Perfect! Let's begin with ${modeNames[selectedMode]}. 🙏`);

    // Inform user about session persistence
    await send(userJid, `*📌Session Mode Lock-in:* Your chosen study mode will remain active for this entire session, allowing for a focused and uninterrupted experience.\n\nTo change modes, simply type "stop study" to end this session, then start a new one.`);

    // Initialize the session with the selected mode
    await SessionModeService.initializeStudyModeSession(session, user, selectedMode);

    // Start the mode-specific flow
    const updatedSession = await SessionService.findActiveSession(user.id);
    if (updatedSession) {
      await this.delegateToModeHandler(updatedSession, user, '', userJid);
    }
  }

  /**
   * Delegate to the appropriate mode-specific handler
   */
  private static async delegateToModeHandler(
    session: StudySession, 
    user: User, 
    message: string, 
    userJid: string
  ): Promise<void> {
    const studyMode = session.study_mode as StudyMode;
    
    switch (studyMode) {
      case 'DEEP_DIVE':
        await this.handleDeepDiveMode(session, user, message, userJid);
        break;
      case 'CHAPTER_OVERVIEW':
        await this.handleChapterOverviewMode(session, user, message, userJid);
        break;
      case 'EXPLORE_THEME':
        await this.handleExploreThemeMode(session, user, message, userJid);
        break;
      default:
        logger.warn({ 
          sessionId: session.id, 
          studyMode 
        }, 'Unknown study mode, falling back to original handler');
        await SessionHandler.handle(session, user, message);
        break;
    }
  }

  /**
   * Handle Deep Dive mode interactions
   */
  private static async handleDeepDiveMode(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    await DeepDiveModeHandler.handle(session, user, message, userJid);
  }

  /**
   * Handle Chapter Overview mode interactions
   */
  private static async handleChapterOverviewMode(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    await ChapterOverviewModeHandler.handle(session, user, message, userJid);
  }

  /**
   * Handle Explore a Theme mode interactions
   */
  private static async handleExploreThemeMode(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    await ExploreThemeModeHandler.handle(session, user, message, userJid);
  }

  /**
   * Handle Confession mode interactions
   */
  private static async handleConfessionMode(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    await ConfessionHandler.handle(session, user, message);
  }
}
