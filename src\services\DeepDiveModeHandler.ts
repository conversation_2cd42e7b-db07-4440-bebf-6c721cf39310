// src/services/DeepDiveModeHandler.ts
// Deep Dive mode implementation for Session Mode Lock-in

import { logger } from '../config/logger.config';
import { StudySession, User, DeepDiveContext } from '../types';
import { SessionService } from './SessionService';
import { StateManager } from './StateManager';
import { AIService } from './AIService';
import { MemoryService } from './MemoryService';
import { messageQueue } from '../queue/messageQueue';
import { ensureMinTypingDelay } from '../utils/messagePacer';

async function send(toJid: string, message: string) {
  await messageQueue.add('send-presence', { toJid, presence: 'composing' });
  
  const typingDelay = Math.min(2500, 800 + message.length * 12);
  const startTime = Date.now();
  
  await ensureMinTypingDelay(startTime, typingDelay);
  await messageQueue.add('send-text', { toJid, text: message });
  await messageQueue.add('send-presence', { toJid, presence: 'paused' });
}

export class DeepDiveModeHandler {
  /**
   * Handle Deep Dive mode interactions
   */
  public static async handle(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as DeepDiveContext;
    const step = session.session_step;

    logger.info({
      sessionId: session.id,
      userId: user.id,
      step,
      studyBlockCount: context?.studyBlockCount || 0
    }, 'Handling Deep Dive mode interaction');

    switch (step) {
      case 'DEEP_DIVE_ACTIVE':
        if (!context.currentVerses || context.currentVerses.length === 0) {
          // Start new study block
          logger.info({
            sessionId: session.id,
            userId: user.id,
            hasCurrentVerses: !!context.currentVerses,
            currentVersesLength: context.currentVerses?.length || 0
          }, 'Starting new Deep Dive study block - no current verses');
          await this.startNewStudyBlock(session, user, userJid);
        } else {
          // Continue with current study block
          logger.info({
            sessionId: session.id,
            userId: user.id,
            currentPassage: context.currentPassage,
            currentVersesLength: context.currentVerses.length
          }, 'Continuing with current Deep Dive study block');
          await this.handleStudyBlockInteraction(session, user, message, userJid);
        }
        break;

      default:
        logger.warn({ sessionId: session.id, step }, 'Unknown Deep Dive step');
        await send(userJid, "I'm not sure how to handle that. Let's continue with our study.");
        break;
    }
  }

  /**
   * Start a new study block with 3-5 verses
   */
  private static async startNewStudyBlock(
    session: StudySession,
    user: User,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as DeepDiveContext;
    const book = context.book;
    const verseBatchSize = 4; // Optimal for Deep Dive mode

    try {
      // Get next verses for study
      const verses = await StateManager.getNextStudyBlock(
        user.id,
        book,
        user.last_completed_chapter || 0,
        user.last_completed_verse || 0,
        verseBatchSize
      );

      if (verses.length === 0) {
        await send(userJid, `🎉 Congratulations! You have completed the book of ${book}! What an incredible journey we've had together.`);
        await SessionService.endSession(session.id, 'COMPLETED');
        return;
      }

      // Format verses for display
      const verseText = verses
        .map(v => `**${v.chapter}:${v.verse}** ${v.text}`)
        .join('\n\n');

      const passageRef = `${book} ${verses[0].chapter}:${verses[0].verse}${verses.length > 1 ? `-${verses[verses.length - 1].verse}` : ''}`;

      // Generate thoughtful reflection question
      const reflectionQuestion = await AIService.generateDeepDiveQuestion(verses);

      // Bundle scripture and question in one message
      const studyMessage = `Let's dive deep into ${passageRef}:\n\n${verseText}\n\n${reflectionQuestion}`;

      await send(userJid, studyMessage);

      // Update context with current study block
      const updatedContext: DeepDiveContext = {
        ...context,
        currentVerses: verses.map(v => ({ chapter: v.chapter, verse: v.verse, text: v.text })),
        currentPassage: passageRef,
        studyBlockCount: (context.studyBlockCount || 0) + 1,
        lastObservation: undefined,
        lastInterpretation: undefined,
        lastApplication: undefined
      };

      await SessionService.updateSessionContext(session.id, updatedContext);

      logger.info({
        sessionId: session.id,
        userId: user.id,
        passageRef,
        studyBlockCount: updatedContext.studyBlockCount
      }, 'Started new Deep Dive study block');

    } catch (error) {
      logger.error({ error, sessionId: session.id }, 'Failed to start new study block');
      await send(userJid, "I'm having trouble getting the next passage. Let me try again in a moment.");
    }
  }

  /**
   * Handle user interaction within a study block
   */
  private static async handleStudyBlockInteraction(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as DeepDiveContext;

    // Store user's response in memory
    await MemoryService.addBibleStudyResponse(
      user.id,
      session.id,
      context.currentPassage || 'Unknown passage',
      message,
      this.determineResponseType(context)
    );

    // Determine conversation stage and respond accordingly
    if (!context.lastObservation) {
      // First response - observation stage
      await this.handleObservationResponse(session, user, message, userJid);
    } else if (!context.lastInterpretation) {
      // Second response - interpretation stage
      await this.handleInterpretationResponse(session, user, message, userJid);
    } else {
      // Third response or continuation - application and next steps
      await this.handleApplicationResponse(session, user, message, userJid);
    }
  }

  /**
   * Handle observation response (first interaction)
   */
  private static async handleObservationResponse(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as DeepDiveContext;

    // Generate affirming response and deeper question
    const aiResponse = await AIService.generateDeepDiveResponse({
      userMessage: message,
      currentPassage: context.currentPassage || '',
      stage: 'observation',
      previousContext: context
    });

    await send(userJid, aiResponse);

    // Update context
    const updatedContext: DeepDiveContext = {
      ...context,
      lastObservation: message
    };

    await SessionService.updateSessionContext(session.id, updatedContext);
  }

  /**
   * Handle interpretation response (second interaction)
   */
  private static async handleInterpretationResponse(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as DeepDiveContext;

    // Generate response that builds on their interpretation
    const aiResponse = await AIService.generateDeepDiveResponse({
      userMessage: message,
      currentPassage: context.currentPassage || '',
      stage: 'interpretation',
      previousContext: context
    });

    await send(userJid, aiResponse);

    // Update context
    const updatedContext: DeepDiveContext = {
      ...context,
      lastInterpretation: message
    };

    await SessionService.updateSessionContext(session.id, updatedContext);
  }

  /**
   * Handle application response and automatically progress to next passage
   */
  private static async handleApplicationResponse(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as DeepDiveContext;

    // Generate encouraging response
    const aiResponse = await AIService.generateDeepDiveResponse({
      userMessage: message,
      currentPassage: context.currentPassage || '',
      stage: 'application',
      previousContext: context
    });

    await send(userJid, aiResponse);

    // Update context with completed passage and clear current verses to trigger new study block
    const updatedContext: DeepDiveContext = {
      ...context,
      completedPassages: [...(context.completedPassages || []), context.currentPassage || ''],
      // Clear current study block data to ensure new verses are fetched and displayed
      currentVerses: [],
      currentPassage: null,
      lastObservation: undefined,
      lastInterpretation: undefined,
      lastApplication: undefined
    };

    await SessionService.updateSessionContext(session.id, updatedContext);

    // Automatically progress to next passage after a brief pause
    await send(userJid, "Let's continue our deep dive journey with the next passage... 📖");

    // Start new study block automatically
    await this.startNewStudyBlock(session, user, userJid);
  }



  /**
   * End Deep Dive session with summary
   */
  private static async endDeepDiveSession(
    session: StudySession,
    user: User,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as DeepDiveContext;
    const sessionHistory = await MemoryService.getBibleStudySessionHistory(session.id);

    // Generate session summary
    const summary = await AIService.generateDeepDiveSessionSummary(
      context.book,
      context.completedPassages || [],
      sessionHistory,
      context.studyBlockCount || 0
    );

    await send(userJid, summary);
    await SessionService.endSession(session.id, 'COMPLETED');

    logger.info({
      sessionId: session.id,
      userId: user.id,
      studyBlockCount: context.studyBlockCount,
      passagesCompleted: context.completedPassages?.length || 0
    }, 'Completed Deep Dive session');
  }

  /**
   * Determine the type of response based on context
   */
  private static determineResponseType(context: DeepDiveContext): 'observation' | 'interpretation' | 'application' {
    if (!context.lastObservation) return 'observation';
    if (!context.lastInterpretation) return 'interpretation';
    return 'application';
  }
}
