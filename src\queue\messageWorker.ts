// src/queue/messageWorker.ts
import { Job, Worker } from 'bullmq';
import { redisConnection } from './connection';
import { EvolutionClient } from '../services/EvolutionClient';
import { logger } from '../config/logger.config';

// Function to process different message job types
async function processMessageJob(job: Job) {
  logger.info({ jobName: job.name, jobId: job.id }, 'Processing job');
  
  switch (job.name) {
    case 'send-text': {
      const { toJid, text } = job.data;
      logger.info({ toJid }, 'Sending text message');
      await EvolutionClient.sendTextMessage(toJid, text);
      break;
    }
    
    case 'send-presence': {
      const { toJid, presence } = job.data;
      logger.info({ toJid, presence }, 'Sending presence state');
      await EvolutionClient.sendPresence(toJid, presence);
      break;
    }
    
    case 'send-audio': {
      const { toJid, audioUrl } = job.data;
      logger.info({ toJid }, 'Sending audio message');
      await EvolutionClient.sendAudioMessage(toJid, audioUrl);
      break;
    }

    case 'send-delayed-bible-study-reminder': {
      const { userId, userJid, userName, scheduledFor } = job.data;
      logger.info({ userId, userJid, scheduledFor }, 'Sending delayed Bible study reminder');

      // Import here to avoid circular dependencies
      const { SessionService } = await import('../services/SessionService');
      const { SessionHandler } = await import('../services/SessionHandler');

      // Create a reminder session and send the reminder
      const session = await SessionService.startSession(userId, { type: 'REMINDER', expiresInMinutes: 60 });
      await SessionHandler.handle(session, { id: userId, jid: userJid, name: userName } as any, '');

      logger.info({ userId }, 'Sent delayed Bible study reminder');
      break;
    }

    default:
      throw new Error(`Unknown job type: ${job.name}`);
  }
}

// Create workers for each queue
export const messageWorker = new Worker(
  'message-queue', // Changed to more descriptive name
  processMessageJob,
  {
    connection: redisConnection,
    concurrency: 5, // Process up to 5 messages at a time
    limiter: { // Don't send more than 1 message every 2 seconds
      max: 1,
      duration: 2000,
    },
  }
);

messageWorker.on('completed', (job) => {
  if (job) {
    logger.info({ jobId: job.id, jobName: job.name }, 'Job completed successfully');
  }
});

messageWorker.on('failed', (job, err) => {
  if (job) {
    logger.error({ jobId: job.id, jobName: job.name, error: err }, 'Job failed after all retries');
  } else {
    logger.error({ error: err }, 'Job failed after all retries (job undefined)');
  }
});