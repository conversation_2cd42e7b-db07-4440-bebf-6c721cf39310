// src/scripts/testSessionModeIntegration.ts
// Integration test for Session Mode Lock-in system

import 'dotenv/config';
import { logger } from '../config/logger.config';
import { SessionModeService } from '../services/SessionModeService';
import { WelcomeService } from '../services/WelcomeService';
import { DeepDiveModeHandler } from '../services/DeepDiveModeHandler';
import { ChapterOverviewModeHandler } from '../services/ChapterOverviewModeHandler';
import { ExploreThemeModeHandler } from '../services/ExploreThemeModeHandler';
import { SessionService } from '../services/SessionService';
import { StateManager } from '../services/StateManager';
import { MemoryService } from '../services/MemoryService';
import { shouldUseSessionModeLockIn, getFeatureFlags } from '../config/features.config';
import { User, StudySession } from '../types';

async function testSessionModeIntegration() {
  logger.info('Starting Session Mode Lock-in integration test...');

  try {
    // 1. Test feature flags
    logger.info('Testing feature flags...');
    const flags = getFeatureFlags();
    logger.info({ flags }, 'Feature flags loaded');
    
    if (!shouldUseSessionModeLockIn()) {
      logger.warn('Session Mode Lock-in is disabled. Enable it to test the new system.');
      return;
    }

    // 2. Test mode selection parsing
    logger.info('Testing mode selection parsing...');
    const testChoices = ['1', '2', '3', 'deep dive', 'chapter overview', 'explore theme'];
    for (const choice of testChoices) {
      const parsed = SessionModeService.parseStudyModeChoice(choice);
      logger.info({ choice, parsed }, 'Mode choice parsed');
    }

    // 3. Test mode menu generation
    logger.info('Testing mode menu generation...');
    const modeMenu = SessionModeService.generateModeSelectionMenu();
    logger.info({ modeMenu }, 'Mode menu generated');

    // 4. Test welcome service
    logger.info('Testing welcome service...');
    const testUser: User = {
      id: 'test-user-id',
      user_hash: 'test-hash-123',
      jid: '<EMAIL>',
      name: 'Test User',
      reminder_time_pref: null,
      user_timezone: 'UTC',
      current_book: 'John',
      last_completed_chapter: 2,
      last_completed_verse: 15,
      current_streak: 0,
      onboarding_step: 'ONBOARDING_COMPLETE',
      is_active: true,
      is_verified: true,
      next_reminder_at: null,
      next_check_in_at: null,
      preferred_study_mode: null,
      total_study_sessions: 5,
      last_study_mode_used: 'DEEP_DIVE',
      last_theme_explored: 'love',
      created_at: new Date(),
      last_interaction_at: new Date()
    };

    const isNewUser = await WelcomeService.isNewUserForBook(testUser);
    logger.info({ isNewUser }, 'New user check completed');

    const welcomeMessage = await WelcomeService.generatePersonalizedWelcome(testUser);
    logger.info({ welcomeMessage }, 'Welcome message generated');

    const modeRecommendation = await WelcomeService.generateModeRecommendation(testUser);
    logger.info({ modeRecommendation }, 'Mode recommendation generated');

    // 5. Test memory service enhancements
    logger.info('Testing enhanced memory service...');
    
    // Store some test data
    await MemoryService.storeSessionModePreference(testUser.id, 'DEEP_DIVE');
    const storedPreference = await MemoryService.getSessionModePreference(testUser.id);
    logger.info({ storedPreference }, 'Session mode preference stored and retrieved');

    // Test theme exploration storage
    await MemoryService.addThemeExploration(
      testUser.id,
      'love',
      [
        { book: 'John', chapter: 3, verse: 16, text: 'For God so loved the world...' },
        { book: '1 John', chapter: 4, verse: 8, text: 'God is love.' }
      ],
      'This shows how God\'s love is both sacrificial and essential to His nature.'
    );

    const themeExplorations = await MemoryService.getThemeExplorations(testUser.id);
    logger.info({ themeExplorations }, 'Theme explorations stored and retrieved');

    // 6. Test state manager enhancements
    logger.info('Testing enhanced state manager...');
    
    await StateManager.updateStudyModeUsage(testUser.id, 'DEEP_DIVE');
    await StateManager.updateLastThemeExplored(testUser.id, 'faith');
    
    const preferences = await StateManager.getUserStudyPreferences(testUser.id);
    logger.info({ preferences }, 'User study preferences retrieved');

    // 7. Test session initialization
    logger.info('Testing session initialization...');
    
    const mockSession: StudySession = {
      id: 'test-session-id',
      user_id: testUser.id,
      session_type: 'BIBLE_STUDY',
      status: 'ACTIVE',
      session_context: {
        book: 'John',
        studyMode: 'DEEP_DIVE',
        sessionStartTime: new Date().toISOString()
      },
      session_step: 'AWAITING_MODE_SELECTION',
      study_mode: null,
      prompt_version: null,
      created_at: new Date(),
      updated_at: new Date(),
      expires_at: new Date(Date.now() + 60 * 60 * 1000) // 1 hour from now
    };

    // Test mode initialization for each mode
    const modes: Array<'DEEP_DIVE' | 'CHAPTER_OVERVIEW' | 'EXPLORE_THEME'> = ['DEEP_DIVE', 'CHAPTER_OVERVIEW', 'EXPLORE_THEME'];
    
    for (const mode of modes) {
      logger.info(`Testing ${mode} mode initialization...`);
      
      try {
        const context = await SessionModeService.initializeStudyModeSession(
          mockSession,
          testUser,
          mode
        );
        logger.info({ mode, context }, `${mode} mode initialized successfully`);
      } catch (error) {
        logger.error({ error, mode }, `Failed to initialize ${mode} mode`);
      }
    }

    // 8. Test database schema compatibility
    logger.info('Testing database schema compatibility...');
    
    // Test that all new columns exist and can be queried
    const userWithNewFields = await StateManager.findUserByJid(testUser.jid);
    if (userWithNewFields) {
      logger.info({
        hasPreferredStudyMode: 'preferred_study_mode' in userWithNewFields,
        hasTotalStudySessions: 'total_study_sessions' in userWithNewFields,
        hasLastStudyModeUsed: 'last_study_mode_used' in userWithNewFields,
        hasLastThemeExplored: 'last_theme_explored' in userWithNewFields
      }, 'Database schema compatibility check');
    }

    // 9. Test error handling
    logger.info('Testing error handling...');
    
    // Test invalid mode selection
    const invalidMode = SessionModeService.parseStudyModeChoice('invalid choice');
    logger.info({ invalidMode }, 'Invalid mode choice handled');

    // Test empty theme exploration
    const emptyThemeExplorations = await MemoryService.getThemeExplorations('non-existent-user');
    logger.info({ emptyThemeExplorations }, 'Empty theme explorations handled');

    // 10. Performance test
    logger.info('Running performance test...');
    
    const startTime = Date.now();
    
    // Simulate rapid mode selections
    for (let i = 0; i < 10; i++) {
      SessionModeService.parseStudyModeChoice('1');
      SessionModeService.generateModeSelectionMenu();
    }
    
    const endTime = Date.now();
    logger.info({ duration: endTime - startTime }, 'Performance test completed');

    logger.info('✅ Session Mode Lock-in integration test completed successfully!');

  } catch (error) {
    logger.error({ error }, '❌ Session Mode Lock-in integration test failed');
    throw error;
  }
}

// Run test if this script is executed directly
if (require.main === module) {
  testSessionModeIntegration()
    .then(() => {
      logger.info('Integration test script completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error({ error }, 'Integration test script failed');
      process.exit(1);
    });
}

export { testSessionModeIntegration };
