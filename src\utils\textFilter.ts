// src/utils/textFilter.ts
// Text filtering utilities for cleaning AI-generated responses

/**
 * Remove em-dashes and other unwanted characters from text
 * This ensures consistent formatting across all bot responses
 */
export function filterText(text: string): string {
  if (!text) return text;
  
  return text
    // Remove em-dashes (—) and replace with regular hyphens (-)
    .replace(/—/g, '-')
    // Remove any other problematic Unicode dashes
    .replace(/–/g, '-') // en-dash
    .replace(/―/g, '-') // horizontal bar
    // Normalize multiple spaces to single spaces
    .replace(/\s+/g, ' ')
    // Trim whitespace
    .trim();
}

/**
 * Filter text specifically for AI responses
 * Applies all necessary cleaning for user-facing content
 */
export function filterAIResponse(response: string): string {
  if (!response) return response;
  
  return filterText(response);
}

/**
 * Filter text for Bible study responses
 * Includes additional cleaning for study-specific content
 */
export function filterBibleStudyResponse(response: string): string {
  if (!response) return response;
  
  return filterText(response);
}
