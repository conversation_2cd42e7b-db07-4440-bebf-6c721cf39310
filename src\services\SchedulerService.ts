// src/services/SchedulerService.ts
import cron from 'node-cron';
import { logger } from '../config/logger.config';
import { SessionService } from './SessionService';
import { StateManager } from './StateManager';
import { SessionHandler } from './SessionHandler';
import { messageQueue } from '../queue/messageQueue';

export class SchedulerService {
  public static start() {
    logger.info('⏰ SchedulerService started.');

    // Runs every 5 minutes
    cron.schedule('*/5 * * * *', async () => {
      try {
        const now = new Date();

        // ----------- CONFESSION REMINDERS (PRIORITY) -------------
        const confessionsDue = await StateManager.getConfessionsDueForReminder(now);
        const usersWithConfessionReminders = new Set<string>();

        for (const { confession, user } of confessionsDue) {
          // Send personalized confession reminder
          const reminderMessage = `Hello ${user.name || 'friend'}, it's time for your reflection:\n\n"${confession.refined_text}"\n\nMay this serve as a gentle moment for prayer and growth.`;

          await messageQueue.add('send-text', {
            toJid: user.jid,
            text: reminderMessage
          });

          // Mark reminder as sent
          await StateManager.markConfessionReminderSent(confession.id);

          // Track users who received confession reminders to avoid duplicate Bible study reminders
          usersWithConfessionReminders.add(user.id);

          logger.info({
            userId: user.id,
            confessionId: confession.id
          }, 'Sent confession reminder');
        }

        // ----------- BIBLE STUDY REMINDERS -------------
        // First, get users due for Bible study reminders without resetting their times yet
        const usersForBibleStudyReminder = await StateManager.getUsersForReminders();

        for (const u of usersForBibleStudyReminder) {
          // Check if user already received confession reminder
          if (usersWithConfessionReminders.has(u.id)) {
            // Delay Bible study reminder by 10 minutes instead of skipping
            logger.info({ userId: u.id }, 'Delaying Bible study reminder by 10 minutes due to confession reminder conflict');

            // Schedule delayed Bible study reminder for 10 minutes from now
            const delayedTime = new Date(now.getTime() + 10 * 60 * 1000); // 10 minutes later

            // Add to message queue with delay
            await messageQueue.add('send-delayed-bible-study-reminder', {
              userId: u.id,
              userJid: u.jid,
              userName: u.name,
              scheduledFor: delayedTime
            }, {
              delay: 10 * 60 * 1000 // 10 minutes delay
            });

            // Reset their reminder time for tomorrow as normal
            const nextReminderTime = new Date(u.next_reminder_at!);
            nextReminderTime.setDate(nextReminderTime.getDate() + 1);
            await StateManager.scheduleNextReminder(u.id, nextReminderTime);

            logger.info({ userId: u.id, delayedTime }, 'Scheduled delayed Bible study reminder');
            continue;
          }

          // Send immediate Bible study reminder for users without confession conflicts
          const session = await SessionService.startSession(u.id, { type: 'REMINDER', expiresInMinutes: 60 });
          await SessionHandler.handle(session, u, '');

          // Reset their reminder time for tomorrow
          const nextReminderTime = new Date(u.next_reminder_at!);
          nextReminderTime.setDate(nextReminderTime.getDate() + 1);
          await StateManager.scheduleNextReminder(u.id, nextReminderTime);

          logger.info({ userId: u.id }, 'Sent Bible study reminder and scheduled next one');
        }

        // ----------- CHECK-INS -------------
        const dueUsers = await StateManager.claimDueCheckIns(now);
        for (const u of dueUsers) {
          const active = await SessionService.findActiveSession(u.id);
          if (!active) {
            const session = await SessionService.startSession(u.id, { type: 'CHECK_IN', expiresInMinutes: 60 });
            await SessionHandler.handle(session, u, '');
          }
          if (u.user_timezone) {
            await StateManager.scheduleNextCheckIn(u.id, u.user_timezone);
          }
        }



        // ----------- STUDY SESSION TIMEOUTS -------------
        // First pass: sessions idle >=30 min get a nudge and move to PAUSED step
        const pool = (await import('../database/client')).pool;
        const nudgeQuery = `
          SELECT s.id, s.user_id, s.session_step, u.jid, COALESCE(u.name, 'friend') AS name
          FROM study_sessions s
          JOIN users u ON u.id = s.user_id
          WHERE s.status = 'ACTIVE'
            AND s.session_type = 'BIBLE_STUDY'
            AND s.updated_at <= NOW() - INTERVAL '30 minutes'
            AND s.session_step NOT IN ('STUDY_SESSION_PAUSED', 'SESSION_WRAP_UP')
          LIMIT 100;
        `;
        const { rows: nudgeRows } = await pool.query(nudgeQuery);
        for (const row of nudgeRows) {
          await messageQueue.add('send-text', { toJid: row.jid, text: `Hey ${row.name}, just checking in. Are you still with me? Reply to continue our study.` });
          await SessionService.updateSessionStep(row.id, 'STUDY_SESSION_PAUSED');
        }

        // Second pass: sessions paused for >10 additional minutes get expired
        const expireQuery = `
          SELECT id FROM study_sessions
          WHERE status = 'ACTIVE'
            AND session_type = 'BIBLE_STUDY'
            AND session_step = 'STUDY_SESSION_PAUSED'
            AND updated_at <= NOW() - INTERVAL '10 minutes'
          LIMIT 100;
        `;
        const { rows: expireRows } = await pool.query(expireQuery);
        // Fetch jid for expiring sessions
        if (expireRows.length) {
          const ids = expireRows.map((r: any) => r.id);
          const expInfoQuery = `
            SELECT s.id, u.jid, COALESCE(u.name, 'friend') AS name
            FROM study_sessions s JOIN users u ON u.id = s.user_id WHERE s.id = ANY($1);
          `;
          const { rows: info } = await pool.query(expInfoQuery, [ids]);
          for (const row of info) {
            await messageQueue.add('send-text', { toJid: row.jid, text: `Our study session has gently closed. Feel free to start a new one anytime!` });
            await SessionService.endSession(row.id, 'EXPIRED');
          }
        }
      } catch (error) {
        logger.error({ error }, 'Scheduler tick failed');
      }
    });

    // Expire stale sessions every 30 minutes
    cron.schedule('*/30 * * * *', async () => {
      try {
        await SessionService.expireStaleSessions();
      } catch (error) {
        logger.error({ error }, 'Expire-stale-sessions job failed');
      }
    });
  }
}