# Paro Bible Bot Backend - Architecture Overview

## Table of Contents
1. [System Overview](#system-overview)
2. [Core Architecture](#core-architecture)
3. [Database Schema](#database-schema)
4. [User Journey Flows](#user-journey-flows)
5. [External Services](#external-services)
6. [Queue System](#queue-system)
7. [Memory Architecture](#memory-architecture)
8. [Study Mode System](#study-mode-system)
9. [AI & RAG Integration](#ai--rag-integration)
10. [Feature Flags](#feature-flags)
11. [Recent Improvements & Quality Enhancements](#recent-improvements--quality-enhancements)

## System Overview

Paro Bible Bot is a WhatsApp-based Bible study companion that provides personalized, intelligent Bible study experiences. The backend is built with Node.js/TypeScript and integrates multiple AI services to deliver contextual biblical guidance.

### Key Features
- **Intelligent Onboarding**: Guided user setup with access control
- **Session Mode Lock-in**: Three distinct study modes (Deep Dive, Chapter Overview, Explore Theme)
- **RAG-Powered Responses**: Semantic search through Bible verses using vector embeddings
- **Personalized AI Responses**: Context-aware responses using user profile and study progress
- **Confession System**: Personal reflection and reminder management
- **Automated Scheduling**: Daily reminders and check-ins
- **Voice Message Support**: Audio transcription and response generation
- **Quality Control**: Automatic text filtering and response formatting
- **Memory Management**: Conversation history and user context retention

## Core Architecture

### Entry Point: `src/index.ts`
- **HTTP Server**: Express.js server on port 3000
- **Webhook Endpoint**: `/webhook` - receives all WhatsApp events from Evolution API
- **Health Check**: `/health` - system status monitoring
- **Background Processing**: Immediate webhook acknowledgment with async message processing

### Main Message Flow
```
WhatsApp → Evolution API → /webhook → handleIncomingMessage() → User Journey Routing
```

### Key Services
- **MessageHandler**: Main routing and general Q&A with user context integration
- **OnboardingHandler**: New user setup and verification
- **SessionHandler/SessionHandlerV3**: Bible study session management
- **StateManager**: Database operations and user state management
- **SchedulerService**: Unified reminder and check-in scheduling system (replaces ReminderService)
- **ConfessionHandler**: Personal confession management and reminder scheduling
- **AIService**: Groq integration for response generation with text filtering
- **RAGService**: Voyage AI embeddings and semantic search
- **MemoryService**: Redis-based conversation history and session context management
- **TextFilter**: Response cleaning and formatting utilities

## Database Schema

### Core Tables

#### `users` Table
```sql
- id (UUID, Primary Key)
- user_hash (TEXT, Unique) - SHA256 of WhatsApp JID
- jid (TEXT) - WhatsApp identifier
- name (TEXT) - User's first name
- reminder_time_pref (TIME) - Daily reminder time
- user_timezone (TEXT) - User's timezone
- current_book (TEXT) - Currently studying Bible book
- last_completed_chapter (INT) - Reading progress
- last_completed_verse (INT) - Reading progress
- current_streak (INT) - Study streak counter
- onboarding_step (ENUM) - Current onboarding state
- is_active (BOOLEAN) - Account status
- is_verified (BOOLEAN) - Access code verification
- next_reminder_at (TIMESTAMPTZ) - Scheduled reminder time
- next_check_in_at (TIMESTAMPTZ) - Scheduled check-in time
- preferred_study_mode (TEXT) - User's preferred study mode
- total_study_sessions (INT) - Session counter
- last_study_mode_used (TEXT) - Last used study mode
- last_theme_explored (TEXT) - Last explored theme
```

#### `study_sessions` Table
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key to users)
- session_type (ENUM: 'BIBLE_STUDY', 'CHECK_IN', 'REMINDER')
- status (ENUM: 'ACTIVE', 'PAUSED', 'COMPLETED', 'EXPIRED')
- session_step (ENUM) - Current step in session flow
- session_context (JSONB) - Dynamic session data
- study_mode (TEXT) - Selected study mode
- prompt_version (SMALLINT) - AI prompt version
- created_at (TIMESTAMPTZ)
- updated_at (TIMESTAMPTZ)
- expires_at (TIMESTAMPTZ)
```

#### `confessions` Table
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key to users)
- original_text (TEXT) - User's original confession
- refined_text (TEXT) - AI-refined version
- reminder_time (TIME) - Confession reminder time
- reminder_frequency (TEXT) - Frequency setting
- next_reminder_at (TIMESTAMPTZ) - Next reminder
- is_active (BOOLEAN) - Active status
```

#### `rag_content` Table
```sql
- id (BIGSERIAL, Primary Key)
- book (TEXT) - Bible book name
- chapter (INT) - Chapter number
- verse (INT) - Verse number
- text (TEXT) - Verse content
- translation (TEXT) - Bible translation
- embedding (VECTOR(1024)) - Voyage AI embedding
```

## User Journey Flows

### 1. Onboarding Flow
**Entry Point**: `OnboardingHandler.handle()`
**File**: `src/services/OnboardingHandler.ts`

#### Flow Steps:
1. **NEEDS_ONBOARDING** → Welcome message, request access code
2. **AWAITING_AUTH** → Verify access code (`ACCESS_CODE` env var)
3. **AWAITING_NAME** → Collect user's first name
4. **AWAITING_REMINDER_TIME** → Set daily reminder time
5. **AWAITING_TIMEZONE** → Detect timezone from location
6. **AWAITING_CONFESSION_CHOICE** → Optional confession setup
7. **AWAITING_CONFESSION_INPUT** → Collect confession text (if chosen)
8. **AWAITING_JOURNEY** → Select Bible book to study
9. **ONBOARDING_COMPLETE** → User ready for main features

#### Key Functions:
- `StateManager.findOrCreateUser()` - User creation/retrieval
- `StateManager.updateOnboardingStep()` - Progress tracking
- `StateManager.scheduleNextReminder()` - Reminder scheduling
- `StateManager.scheduleInitialCheckIn()` - Check-in scheduling

### 2. General Q&A Flow
**Entry Point**: `handleGeneralMessage()`
**File**: `src/services/MessageHandler.ts`

#### Flow Steps:
1. **Message Reception** → Extract text from WhatsApp payload
2. **User Lookup** → Find/create user by JID hash
3. **Command Detection** → Check for special commands ('start study', 'stop study', etc.)
4. **RAG Search** → Find relevant Bible verses using semantic search
5. **User Context Integration** → Include user profile information (name, current book, streak)
6. **AI Generation** → Generate contextual response using Groq with user context
7. **Text Filtering** → Clean response text (remove em-dashes, normalize formatting)
8. **Response Delivery** → Send via message queue with typing indicators

#### Key Components:
- **RAGService.search()** → Semantic verse search
- **AIService.generateAnswer()** → Groq-powered response generation with user context
- **PromptService.getContextualPrompt()** → Dynamic prompt construction
- **TextFilter utilities** → Response cleaning and formatting
- **MemoryService** → Conversation history management (6 message limit)
- **Message Queue** → Async delivery with rate limiting

### 3. Bible Study Session Flow
**Entry Point**: `SessionHandlerV3.handle()` (if Session Mode Lock-in enabled)
**File**: `src/services/SessionHandlerV3.ts`

#### Session Initialization:
1. **Command Detection** → 'start study' triggers session creation
2. **Mode Selection** → User chooses study mode (if not set)
3. **Context Setup** → Initialize mode-specific context
4. **Handler Delegation** → Route to appropriate mode handler

#### Session States:
- **AWAITING_MODE_SELECTION** → Waiting for user to choose study mode
- **DEEP_DIVE_ACTIVE** → Active Deep Dive session
- **CHAPTER_OVERVIEW_ACTIVE/WAITING** → Chapter reading session
- **EXPLORE_THEME_ACTIVE/WAITING** → Theme exploration session

### 4. Enhanced Confession System Flow
**Entry Point**: `ConfessionHandler.handle()`
**Files**: `ConfessionHandler.ts`, `OnboardingHandler.ts`, `MessageHandler.ts`

#### Flow Steps:
1. **Confession Input** → User provides personal confession text
2. **AI Refinement** → Groq processes and refines the confession
3. **User Confirmation** → User approves refined version
4. **Reminder Setup** → Enhanced time parsing with timezone support
5. **Conflict Prevention** → Deactivate existing confession reminders
6. **Storage** → Save to confessions table with frequency support
7. **Reminder Delivery** → Priority-based automated reminders via SchedulerService

#### Enhanced Features:
- **Timezone-Aware Parsing** → Accurate time conversion in user's timezone
- **Multiple Frequency Support** → Daily, weekly, monthly, specific days, one-time
- **Single Active Reminder** → Automatic deactivation of old reminders
- **Conflict Resolution** → Priority over Bible study reminders
- **Show Confessions Command** → Retrieve and display stored confessions

### 5. Check-in Flow
**Entry Point**: `SessionHandler.handleCheckIn()`
**File**: `src/services/SessionHandler.ts`

#### Flow Steps:
1. **Scheduled Trigger** → SchedulerService initiates check-in
2. **Session Creation** → Create CHECK_IN type session
3. **Engagement Questions** → AI-generated check-in questions
4. **Response Processing** → Handle user responses
5. **Follow-up Actions** → Encourage study or provide support
6. **Next Scheduling** → Schedule next check-in (random time 10AM-8PM using TimeUtils)

### 6. Unified Reminder & Scheduling Flow
**Entry Point**: `SchedulerService.start()`
**File**: `src/services/SchedulerService.ts`

#### Cron Job Schedule:
- **Main Scheduler**: Every 5 minutes (`*/5 * * * *`)
- **Session Cleanup**: Every 30 minutes (`*/30 * * * *`)

#### Priority-Based Processing:
1. **Confession Reminders (HIGHEST PRIORITY)**
   - Query confessions due for reminder
   - Send personalized confession messages
   - Mark reminders as sent
   - Track users to avoid Bible study conflicts

2. **Bible Study Reminders (CONFLICT RESOLUTION)**
   - Query users due for Bible study reminders
   - Skip users who received confession reminders
   - Delay Bible study reminders by 10 minutes for conflicts
   - Schedule next day's reminder

3. **Check-ins**
   - Query users due for check-ins
   - Create CHECK_IN sessions
   - Schedule next random check-in (10AM-8PM)

4. **Session Cleanup**
   - Expire stale sessions
   - Send closure notifications

### 7. Voice Message Flow
**Entry Point**: `VoiceService.processVoiceMessage()`
**File**: `src/services/VoiceService.ts`

#### Flow Steps:
1. **Audio Reception** → Receive voice message from WhatsApp
2. **Transcription** → Groq Whisper API converts audio to text
3. **Text Processing** → Route transcribed text through normal message flow
4. **Response Generation** → Generate text response
5. **Audio Synthesis** → Convert response to audio using Groq TTS
6. **Storage** → Upload audio to Cloudflare R2
7. **Delivery** → Send audio response via WhatsApp

## External Services

### Evolution API Integration
**File**: `src/services/EvolutionClient.ts`
**Purpose**: WhatsApp Business API interface

#### Methods:
- `sendTextMessage()` → Send text messages
- `sendPresence()` → Send typing indicators
- `sendAudioMessage()` → Send voice messages

### Groq AI Integration
**File**: `src/services/AIService.ts`
**Purpose**: Language model and audio processing

#### Capabilities:
- **Text Generation**: Response generation using various models with user context
- **Audio Transcription**: Whisper API for voice-to-text
- **Text-to-Speech**: Audio response generation
- **Model Selection**: Dynamic model selection based on context
- **User Context Integration**: Personalized responses using user profile data
- **Response Filtering**: Automatic text cleaning and formatting

#### Enhanced Features:
- **Personalized Prompts**: Include user name, current book, and study streak
- **Context-Aware Responses**: Reference user's Bible study progress
- **Text Quality Control**: Remove em-dashes and normalize formatting
- **Memory Integration**: Access to conversation history and user insights

### Voyage AI Integration
**File**: `src/services/RAGService.ts`
**Purpose**: Vector embeddings for semantic search

#### Process:
1. **Query Embedding** → Convert user query to vector
2. **Similarity Search** → Find closest Bible verses
3. **Rate Limiting** → Exponential backoff for 429 errors
4. **Result Filtering** → Distance-based relevance filtering

### Cloudflare R2 Integration
**File**: `src/services/VoiceService.ts`
**Purpose**: Audio file storage

#### Process:
1. **Audio Upload** → Store generated audio files
2. **Public URLs** → Generate accessible URLs for WhatsApp
3. **Cleanup** → Manage storage lifecycle

### Text Filtering System
**File**: `src/utils/textFilter.ts`
**Purpose**: Response quality control and formatting consistency

#### Features:
- **Em-dash Removal** → Replace em-dashes (—) with regular hyphens (-)
- **Unicode Normalization** → Convert en-dashes (–) and horizontal bars (―)
- **Whitespace Cleanup** → Normalize multiple spaces and trim content
- **Response Consistency** → Ensure uniform formatting across all AI responses

#### Implementation:
- **filterText()** → Core text cleaning function
- **filterAIResponse()** → General AI response filtering
- **filterBibleStudyResponse()** → Study-specific response filtering
- **Applied Across Services** → Integrated into all AI response methods

## Queue System

### Message Queue Architecture
**Files**: `src/queue/messageQueue.ts`, `src/queue/messageWorker.ts`
**Technology**: BullMQ with Redis

#### Job Types:
- **send-text** → Text message delivery (with optional delay support)
- **send-presence** → Typing indicator delivery
- **send-audio** → Voice message delivery

#### Configuration:
- **Rate Limiting**: 1 message per 2 seconds
- **Retry Logic**: 3 attempts with exponential backoff
- **Concurrency**: 5 concurrent workers
- **Delay Support**: Job scheduling with delay for conflict resolution
- **Error Handling**: Comprehensive logging and failure tracking

### Message Pacing System
**File**: `src/utils/messagePacer.ts`

#### Features:
- **Dynamic Typing Delays** → Based on message length
- **Chunk Splitting** → Break long messages on `\n\n`
- **Realistic Timing** → Human-like typing simulation
- **Queue Integration** → Seamless integration with BullMQ

## Memory Architecture

### Positional Memory (PostgreSQL)
**Purpose**: Persistent user state and progress tracking
**Storage**: User preferences, reading progress, session history

### Reflective Memory (Redis)
**Purpose**: Conversation context and temporary state
**Storage**: Recent interactions, session scratchpad data
**Implementation**: `MemoryService.ts` with 6-message conversation history limit

#### Memory Features:
- **Conversation History** → Last 6 messages per user (7-day expiry)
- **Bible Study Insights** → Session-specific user responses and insights
- **Chapter Progress** → Reading progress for Chapter Overview mode
- **Theme Exploration** → Cross-biblical theme discussion context
- **User Insights** → Long-term insight storage (28-day expiry)

### Session Context (JSONB)
**Purpose**: Dynamic session-specific data
**Storage**: Mode-specific context, current passage, user responses

#### Context Types:
- **BaseSessionContext** → Common session data
- **DeepDiveContext** → Deep dive specific data
- **ChapterOverviewContext** → Chapter reading data
- **ExploreThemeContext** → Theme exploration data

## Study Mode System

### Session Mode Lock-in Architecture
**File**: `src/services/SessionModeService.ts`
**Purpose**: Focused, mode-specific study experiences

#### Three Study Modes:

### 1. Deep Dive Mode
**Handler**: `src/services/DeepDiveModeHandler.ts`
**Purpose**: Intensive verse-by-verse study

#### Process:
1. **Verse Retrieval** → Get next 2-4 verses from current book
2. **Observation Stage** → User shares initial observations
3. **Interpretation Stage** → Deeper meaning exploration
4. **Application Stage** → Personal application discussion
5. **Progress Tracking** → Update reading progress
6. **Continuation** → Move to next passage or conclude

### 2. Chapter Overview Mode
**Handler**: `src/services/ChapterOverviewModeHandler.ts`
**Purpose**: Complete chapter reading with discussion

#### Process:
1. **Chapter Chunking** → Break chapter into readable sections (400-900 chars)
2. **Sequential Delivery** → Send chunks with user pacing
3. **Reading Confirmation** → Wait for "ready" or "next" signals
4. **Chapter Discussion** → AI-generated discussion questions
5. **Progress Update** → Mark chapter as completed
6. **Next Chapter** → Offer to continue or conclude

### 3. Explore Theme Mode
**Handler**: `src/services/ExploreThemeModeHandler.ts`
**Purpose**: Cross-biblical theme exploration

#### Process:
1. **Theme Selection** → User chooses topic of interest
2. **RAG Search** → Find relevant verses across Bible
3. **Verse Curation** → Select 3-5 verses from different books
4. **Theme Discussion** → Explore common threads and insights
5. **Synthesis** → AI-generated thematic connections
6. **Application** → Personal relevance and application

## AI & RAG Integration

### Prompt Engineering
**File**: `src/services/PromptService.ts`
**Purpose**: Dynamic prompt construction for different contexts

#### Prompt Types:
- **General Conversation** → Q&A responses with StyleGuide
- **Deep Dive** → Mode-specific prompts for each stage
- **Chapter Overview** → Reading comprehension prompts
- **Theme Exploration** → Cross-reference and synthesis prompts

#### StyleGuide Features:
- **Varied Introductions** → 8 different biblical introduction patterns
- **Mobile Optimization** → Response structure for WhatsApp
- **Length Constraints** → Character limits for readability
- **Engagement Focus** → Single, clear questions

### RAG (Retrieval-Augmented Generation)
**File**: `src/services/RAGService.ts`
**Purpose**: Contextual Bible verse retrieval

#### Process:
1. **Query Processing** → Clean and prepare user query
2. **Embedding Generation** → Voyage AI vector creation
3. **Similarity Search** → PostgreSQL vector search with pgvector
4. **Result Ranking** → Distance-based relevance scoring
5. **Context Integration** → Inject relevant verses into AI prompts

## Feature Flags

### Configuration
**File**: `src/config/features.config.ts`
**Environment Variables**:
- `ENABLE_SESSION_MODE_LOCK_IN` → Enable new study system
- `ENABLE_DEEP_DIVE_MODE` → Enable Deep Dive mode
- `ENABLE_CHAPTER_OVERVIEW_MODE` → Enable Chapter Overview mode
- `ENABLE_EXPLORE_THEME_MODE` → Enable Explore Theme mode

### Implementation
- **Runtime Switching** → Dynamic feature enabling/disabling
- **Backward Compatibility** → Fallback to original SessionHandler
- **Gradual Rollout** → Safe feature deployment

## Recent Improvements & Quality Enhancements

### Unified Scheduler System (2025-07)
**Purpose**: Consolidated reminder and scheduling management
**Files**: `SchedulerService.ts`, `StateManager.ts`

#### Major Changes:
- **ReminderService Replacement** → SchedulerService now handles all scheduling
- **Priority-Based Processing** → Confession reminders take priority over Bible study
- **Conflict Resolution** → 10-minute delay for conflicting reminders on same day
- **Cron Job Architecture** → 5-minute intervals for reminder checks, 30-minute for cleanup
- **Enhanced Logging** → Comprehensive tracking of all scheduled activities

### Enhanced Confession System (2025-07)
**Purpose**: Robust confession reminder management with timezone accuracy
**Files**: `ConfessionHandler.ts`, `ConfessionUtils.ts`, `StateManager.ts`

#### Improvements:
- **Timezone-Aware Parsing** → Fixed 3pm→4:05pm conversion issues using Luxon DateTime
- **Multiple Frequency Support** → Daily, weekly, monthly, specific days, one-time reminders
- **Single Active Reminder Enforcement** → Automatic deactivation of old confession reminders
- **Enhanced Time Parsing** → Direct timezone creation to avoid double conversion
- **Debugging Support** → Detailed logging for timezone conversion tracking
- **Show Confessions Command** → Dedicated command handler for retrieving stored confessions

### Reminder Conflict Resolution (2025-07)
**Purpose**: Intelligent handling of simultaneous reminder conflicts
**Implementation**: `SchedulerService.ts`

#### Features:
- **Priority System** → Confession reminders sent immediately, Bible study delayed
- **10-Minute Delay** → Bible study reminders delayed by 10 minutes when conflicts occur
- **Same-Day Delivery** → No reminders lost to next-day scheduling
- **User Tracking** → Prevents duplicate reminders for users with confession conflicts
- **Queue Integration** → Uses message queue delay feature for timed delivery

### User Context Integration (2025-01)
**Purpose**: Enhanced personalization and memory retention
**Files**: `AIService.ts`, `MessageHandler.ts`

#### Improvements:
- **Memory Context Loss Fix** → AI now receives user profile information
- **Personalized Responses** → Include user name, current book, and study streak
- **Enhanced Prompts** → Dynamic prompt construction with user context
- **Non-destructive Implementation** → Backward compatible with existing functionality

### Text Quality Control System (2025-01)
**Purpose**: Consistent response formatting and quality
**Files**: `textFilter.ts`, `AIService.ts`

#### Features:
- **Em-dash Elimination** → Systematic removal of problematic Unicode characters
- **Response Normalization** → Consistent formatting across all AI responses
- **Quality Assurance** → Applied to all AI response methods
- **Performance Optimized** → Minimal impact on response times

### Typing Animation Verification (2025-01)
**Status**: Confirmed working correctly
**Evidence**: Production logs show proper presence state management

#### Functionality:
- **Composing State** → Sent before message generation
- **Paused State** → Sent after message delivery
- **Queue Integration** → Seamless BullMQ job processing
- **Error Handling** → Comprehensive job completion tracking

## Deployment & Operations

### Environment Configuration
**File**: `.env.example`
**Required Variables**:
- Database: `DATABASE_URL`, `REDIS_URL`
- APIs: `GROQ_API_KEY`, `VOYAGE_AI_API_KEY`, `EVOLUTION_API_KEY`
- Security: `ACCESS_CODE`, `EVOLUTION_WEBHOOK_SECRET`
- Storage: `R2_ACCESS_KEY_ID`, `R2_SECRET_ACCESS_KEY`
- Timezone: Proper timezone configuration for accurate reminder scheduling

### Monitoring & Logging
**File**: `src/config/logger.config.ts`
**Features**:
- **Structured Logging** → JSON format with Pino
- **Request Tracking** → User ID and session correlation
- **Error Handling** → Comprehensive error logging
- **Performance Metrics** → Response time tracking

### Database Management
**Files**: `src/database/schema.ts`, `src/scripts/`
**Features**:
- **Schema Migrations** → Idempotent schema updates
- **Data Seeding** → RAG content population
- **Backup Strategies** → Automated backup procedures
- **Performance Optimization** → Index management

## Detailed Flow Diagrams

### Complete User Message Processing Flow
```
WhatsApp Message → Evolution API → /webhook → handleIncomingMessage()
    ↓
User Lookup (StateManager.findOrCreateUser())
    ↓
Onboarding Check (user.onboarding_step !== 'ONBOARDING_COMPLETE')
    ↓ (if incomplete)
OnboardingHandler.handle() → [Onboarding Flow Steps]
    ↓ (if complete)
Active Session Check (SessionService.findActiveSession())
    ↓ (if active session)
SessionHandlerV3.handle() → [Study Mode Routing]
    ↓ (if no active session)
Command Detection ('start study', 'stop study', 'show confessions', etc.)
    ↓ (if general message)
handleGeneralMessage() → RAG Search → User Context Integration → AI Generation → Text Filtering → Response
```

### SchedulerService Cron Job Flow
```
Every 5 minutes → SchedulerService cron job
    ↓
1. CONFESSION REMINDERS (Priority)
   StateManager.getConfessionsDueForReminder() → Send reminders → Track users
    ↓
2. BIBLE STUDY REMINDERS (Conflict Resolution)
   StateManager.getUsersForReminders() → Skip confession users → Send or delay
    ↓
3. CHECK-INS
   StateManager.claimDueCheckIns() → Create sessions → Schedule next
    ↓
4. SESSION CLEANUP
   Query expired sessions → Send closure messages → End sessions
```

### Session Mode Lock-in Flow
```
'start study' command → SessionModeService.handleSessionStart()
    ↓
Create session with AWAITING_MODE_SELECTION
    ↓
Present mode options (1. Deep Dive, 2. Chapter Overview, 3. Explore Theme)
    ↓
User selects mode → SessionModeService.parseStudyModeChoice()
    ↓
Initialize mode-specific context → SessionModeService.initializeStudyModeSession()
    ↓
Route to appropriate handler:
    - DeepDiveModeHandler.handle()
    - ChapterOverviewModeHandler.handle()
    - ExploreThemeModeHandler.handle()
```

### RAG Search Process
```
User Query → RAGService.search()
    ↓
Generate embedding (Voyage AI) → generateEmbeddingWithRetry()
    ↓
Vector similarity search (PostgreSQL + pgvector)
    ↓
Distance filtering (> 0.35 threshold)
    ↓
Return relevant verses → AIService.generateAnswer()
    ↓
Inject user context (name, current book, streak) → Enhanced prompt
    ↓
Inject verses into prompt → Groq API
    ↓
Generate contextual response → Text filtering
    ↓
Clean and formatted response
```

## Error Handling & Recovery

### Rate Limiting Strategy
**Voyage AI Rate Limits**:
- **Detection**: 429 status code monitoring
- **Retry Logic**: Exponential backoff (2s, 4s, 8s)
- **Fallback**: Graceful degradation to general knowledge
- **Logging**: Detailed rate limit tracking

### Message Queue Resilience
**BullMQ Configuration**:
- **Retry Attempts**: 3 attempts per job
- **Backoff Strategy**: Exponential (5s, 10s, 20s)
- **Dead Letter Queue**: Failed job storage
- **Circuit Breaker**: Automatic failure detection

### Database Connection Management
**PostgreSQL Resilience**:
- **Connection Pooling**: Automatic connection management
- **Transaction Safety**: ACID compliance for critical operations
- **Backup Strategies**: Automated daily backups
- **Migration Safety**: Idempotent schema updates

## Performance Optimization

### Caching Strategy
**Redis Implementation**:
- **Session Context**: Temporary session data
- **User Preferences**: Frequently accessed user data
- **RAG Results**: Cache recent search results
- **Rate Limiting**: API call tracking

### Database Optimization
**Index Strategy**:
- **Vector Search**: HNSW index on embeddings
- **User Lookup**: Hash-based user retrieval
- **Session Queries**: Composite indexes on user_id + status
- **Temporal Queries**: Indexes on timestamp fields

### Message Processing Optimization
**Queue Management**:
- **Batch Processing**: Group similar operations
- **Priority Queues**: Urgent vs. standard messages
- **Load Balancing**: Multiple worker instances
- **Memory Management**: Efficient job cleanup

## Security Considerations

### Access Control
**Authentication**:
- **Access Code**: Environment-based verification
- **User Verification**: One-time code validation
- **Session Security**: UUID-based session tokens
- **JID Hashing**: SHA256 phone number protection

### Data Protection
**Privacy Measures**:
- **PII Encryption**: Sensitive data encryption
- **Data Retention**: Configurable retention policies
- **Audit Logging**: Comprehensive access logging
- **GDPR Compliance**: Data deletion capabilities

### API Security
**External Service Protection**:
- **API Key Rotation**: Regular key updates
- **Rate Limit Monitoring**: Abuse detection
- **Request Validation**: Input sanitization
- **Error Masking**: Sensitive error information hiding

## Monitoring & Observability

### Logging Strategy
**Structured Logging**:
- **Request Correlation**: Trace user journeys
- **Performance Metrics**: Response time tracking
- **Error Aggregation**: Centralized error collection
- **Business Metrics**: User engagement tracking

### Health Monitoring
**System Health**:
- **Database Connectivity**: Connection pool monitoring
- **Queue Health**: Job processing rates
- **External API Status**: Service availability tracking
- **Memory Usage**: Resource consumption monitoring

### Alerting System
**Critical Alerts**:
- **High Error Rates**: Automatic notification
- **Queue Backlog**: Processing delay alerts
- **Database Issues**: Connection failure alerts
- **API Rate Limits**: Service degradation warnings

## Key Architectural Improvements (2025-07)

### Unified Scheduling Architecture
- **Consolidated Services**: SchedulerService replaces multiple reminder services
- **Priority-Based Processing**: Confession reminders prioritized over Bible study
- **Intelligent Conflict Resolution**: 10-minute delays instead of next-day scheduling
- **Enhanced Timezone Support**: Accurate time parsing and conversion using Luxon

### Robust Confession Management
- **Single Active Reminder**: Automatic deactivation prevents duplicate reminders
- **Multiple Frequency Support**: Daily, weekly, monthly, specific days, one-time
- **Timezone-Aware Parsing**: Fixed conversion issues for accurate scheduling
- **Enhanced User Experience**: "Show confessions" command for easy retrieval

### Improved Message Queue System
- **Delay Support**: Job scheduling with delay for conflict resolution
- **Enhanced Reliability**: Better error handling and retry logic
- **Performance Optimization**: Efficient queue management for scheduled messages

This comprehensive architecture provides a robust, scalable foundation for delivering personalized Bible study experiences through WhatsApp, with detailed user journey management, intelligent AI integration, reliable message delivery systems, unified scheduling capabilities, and enterprise-grade operational capabilities.
