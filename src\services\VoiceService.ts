// src/services/VoiceService.ts
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import Groq from 'groq-sdk';
import { validateEnv } from '../config/env.config';
import { logger } from '../config/logger.config';
import { redisConnection } from '../queue/connection';
import { randomUUID } from 'crypto';

const env = validateEnv();
const groq = new Groq({ apiKey: env.GROQ_API_KEY });

// Configure S3 client for Cloudflare R2
const s3 = new S3Client({
  region: 'auto',
  endpoint: `https://${env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: env.R2_ACCESS_KEY_ID!,
    secretAccessKey: env.R2_SECRET_ACCESS_KEY!,
  },
});

const TTS_BUDGET_KEY = 'tts_requests_today';
const DAILY_TTS_LIMIT = 95; // Stay safely under the 100 limit

export class VoiceService {
  private static async checkAndIncrementBudget(): Promise<boolean> {
    const currentCount = await redisConnection.get(TTS_BUDGET_KEY);
    if (currentCount && parseInt(currentCount, 10) >= DAILY_TTS_LIMIT) {
      return false; // Budget exceeded
    }
    const pipeline = redisConnection.pipeline();
    pipeline.incr(TTS_BUDGET_KEY);
    pipeline.expire(TTS_BUDGET_KEY, 60 * 60 * 24); // 24-hour expiry
    await pipeline.exec();
    return true; // Budget available
  }

  public static async textToSpeech(text: string): Promise<Buffer | null> {
    const budgetAvailable = await this.checkAndIncrementBudget();
    if (!budgetAvailable) {
      logger.warn('Daily TTS quota exceeded. Skipping TTS generation.');
      throw new Error('TTS_QUOTA_EXCEEDED');
    }

    try {
      const response = await groq.audio.speech.create({
        model: 'playai-tts', // As per your new goal
        voice: 'Mitch-PlayAI', // As per your new goal
        input: text,
        speed: 1.0
      });

      const audioBuffer = Buffer.from(await response.arrayBuffer());
      return audioBuffer;
    } catch (error) {
      logger.error({ error }, 'Groq TTS API call failed');
      if ((error as any)?.error?.error?.code === 'model_terms_required') {
        logger.error('Groq terms not accepted for playai-tts. Visit https://console.groq.com/playground?model=playai-tts to accept them.');
      }
      throw error;
    }
  }

  public static async uploadAudioToR2(audioBuffer: Buffer, userId: string): Promise<string> {
    const fileName = `voice-notes/${userId}/${randomUUID()}.mp3`;
    
    const command = new PutObjectCommand({
      Bucket: env.R2_BUCKET_NAME!,
      Key: fileName,
      Body: audioBuffer,
      ContentType: 'audio/mpeg',
    });

    try {
      await s3.send(command);
      const publicUrl = `${env.R2_PUBLIC_URL}/${fileName}`;
      logger.info({ userId, url: publicUrl }, 'Successfully uploaded audio to R2');
      return publicUrl;
    } catch (error) {
      logger.error({ error }, 'Failed to upload audio to R2');
      throw error;
    }
  }
}