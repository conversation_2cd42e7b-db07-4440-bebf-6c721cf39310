// src/services/MemoryService.ts
import { ChatMessage } from '../types';
import Redis from 'ioredis';
import { logger } from '../config/logger.config';
import { redisConnection } from '../queue/connection';

// Redis client setup
//const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
// const redis = new Redis(redisUrl);

export class MemoryService {
  private static readonly HISTORY_KEY_PREFIX = 'chat_history:';
  private static readonly BIBLE_STUDY_KEY_PREFIX = 'bible_study:';
  private static readonly MAX_MESSAGES = 6; // Keep last 6 messages for context
  private static readonly EXPIRY_SECONDS = 60 * 60 * 24 * 7; // 7 days

  /**
   * Add a message to the user's conversation history
   */
  public static async addMessageToHistory(userId: string, message: ChatMessage): Promise<void> {
    const key = this.HISTORY_KEY_PREFIX + userId;
    
    try {
      // Add message to Redis list
      await redisConnection.rpush(key, JSON.stringify(message));
      
      // Trim to keep only the most recent messages
      await redisConnection.ltrim(key, -this.MAX_MESSAGES, -1);
      
      // Set expiry to prevent unlimited growth
      await redisConnection.expire(key, this.EXPIRY_SECONDS);
      
      logger.info({ userId }, 'Added message to conversation history.');
    } catch (error) {
      logger.error({ error, userId }, 'Failed to add message to conversation history');
      throw error;
    }
  }

  /**
   * Get the user's conversation history
   */
  public static async getHistory(userId: string): Promise<ChatMessage[]> {
    const key = this.HISTORY_KEY_PREFIX + userId;
    
    try {
      const messages = await redisConnection.lrange(key, 0, -1);
      return messages.map(msg => JSON.parse(msg) as ChatMessage);
    } catch (error) {
      logger.error({ error, userId }, 'Failed to retrieve conversation history');
      return [];
    }
  }

  /**
   * Add a Bible study response to the session memory
   */
  public static async addBibleStudyResponse(
    userId: string,
    sessionId: string,
    passage: string,
    userResponse: string,
    questionType: 'observation' | 'interpretation' | 'application'
  ): Promise<void> {
    const sessionKey = `${this.BIBLE_STUDY_KEY_PREFIX}${sessionId}`;
    const userInsightKey = `${this.BIBLE_STUDY_KEY_PREFIX}insights:${userId}`;
    
    const insight = {
      passage,
      response: userResponse,
      type: questionType,
      timestamp: new Date().toISOString()
    };
    
    try {
      // Add to session-specific memory
      await redisConnection.rpush(sessionKey, JSON.stringify(insight));
      await redisConnection.expire(sessionKey, this.EXPIRY_SECONDS);
      
      // Add to long-term user insights memory
      await redisConnection.rpush(userInsightKey, JSON.stringify({
        ...insight,
        sessionId
      }));
      await redisConnection.expire(userInsightKey, this.EXPIRY_SECONDS * 4); // Keep insights longer
      
      logger.info({ userId, sessionId, questionType }, 'Added Bible study response to memory');
    } catch (error) {
      logger.error({ error, userId, sessionId }, 'Failed to add Bible study response');
      throw error;
    }
  }

  /**
   * Get the Bible study history for a specific session
   */
  public static async getBibleStudySessionHistory(
    sessionId: string
  ): Promise<Array<{passage: string, response: string, type: string, timestamp: string}>> {
    const key = `${this.BIBLE_STUDY_KEY_PREFIX}${sessionId}`;
    
    try {
      const responses = await redisConnection.lrange(key, 0, -1);
      return responses.map(item => JSON.parse(item));
    } catch (error) {
      logger.error({ error, sessionId }, 'Failed to retrieve Bible study session history');
      return [];
    }
  }

  /**
   * Get the user's past Bible study insights, optionally filtered by book
   */
  public static async getUserBibleInsights(
    userId: string,
    book?: string
  ): Promise<Array<{passage: string, response: string, type: string, timestamp: string, sessionId: string}>> {
    const key = `${this.BIBLE_STUDY_KEY_PREFIX}insights:${userId}`;
    
    try {
      const insights = await redisConnection.lrange(key, 0, -1);
      const parsedInsights = insights.map(item => JSON.parse(item));
      
      // Filter by book if specified
      if (book) {
        return parsedInsights.filter(insight => insight.passage.startsWith(book));
      }
      
      return parsedInsights;
    } catch (error) {
      logger.error({ error, userId }, 'Failed to retrieve user Bible insights');
      return [];
    }
  }

  /**
   * Get the most recent meaningful insight for welcome back message
   */
  public static async getRecentInsightForWelcome(
    userId: string,
    book?: string
  ): Promise<{passage: string, response: string, type: string, timestamp: string} | null> {
    try {
      const insights = await this.getUserBibleInsights(userId, book);

      if (insights.length === 0) {
        return null;
      }

      // Find the most recent meaningful insight (prefer application over observation)
      const meaningfulInsights = insights
        .filter(insight => insight.response.length > 20) // Filter out very short responses
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      // Prefer application insights, then interpretation, then observation
      const applicationInsight = meaningfulInsights.find(i => i.type === 'application');
      const interpretationInsight = meaningfulInsights.find(i => i.type === 'interpretation');
      const observationInsight = meaningfulInsights.find(i => i.type === 'observation');

      return applicationInsight || interpretationInsight || observationInsight || meaningfulInsights[0] || null;
    } catch (error) {
      logger.error({ error, userId }, 'Failed to get recent insight for welcome');
      return null;
    }
  }

  /**
   * Store session mode preference in Redis for quick access
   */
  public static async storeSessionModePreference(userId: string, studyMode: string): Promise<void> {
    const key = `session_mode_pref:${userId}`;
    try {
      await redisConnection.setex(key, 60 * 60 * 24 * 30, studyMode); // 30 days
      logger.info({ userId, studyMode }, 'Stored session mode preference');
    } catch (error) {
      logger.error({ error, userId, studyMode }, 'Failed to store session mode preference');
    }
  }

  /**
   * Get session mode preference from Redis
   */
  public static async getSessionModePreference(userId: string): Promise<string | null> {
    const key = `session_mode_pref:${userId}`;
    try {
      return await redisConnection.get(key);
    } catch (error) {
      logger.error({ error, userId }, 'Failed to get session mode preference');
      return null;
    }
  }

  /**
   * Store theme exploration history
   */
  public static async addThemeExploration(
    userId: string,
    theme: string,
    verses: Array<{book: string, chapter: number, verse: number, text: string}>,
    userInsight: string
  ): Promise<void> {
    const key = `theme_explorations:${userId}`;
    const exploration = {
      theme,
      verses,
      userInsight,
      timestamp: new Date().toISOString()
    };

    try {
      await redisConnection.rpush(key, JSON.stringify(exploration));
      await redisConnection.ltrim(key, -10, -1); // Keep last 10 explorations
      await redisConnection.expire(key, this.EXPIRY_SECONDS * 2); // Keep longer than regular insights

      logger.info({ userId, theme }, 'Added theme exploration to memory');
    } catch (error) {
      logger.error({ error, userId, theme }, 'Failed to add theme exploration');
    }
  }

  /**
   * Get theme exploration history
   */
  public static async getThemeExplorations(userId: string): Promise<Array<{
    theme: string,
    verses: Array<{book: string, chapter: number, verse: number, text: string}>,
    userInsight: string,
    timestamp: string
  }>> {
    const key = `theme_explorations:${userId}`;

    try {
      const explorations = await redisConnection.lrange(key, 0, -1);
      return explorations.map(item => JSON.parse(item));
    } catch (error) {
      logger.error({ error, userId }, 'Failed to retrieve theme explorations');
      return [];
    }
  }

  /**
   * Store chapter reading progress for Chapter Overview mode
   */
  public static async storeChapterProgress(
    userId: string,
    sessionId: string,
    book: string,
    chapter: number,
    chunksRead: number,
    totalChunks: number
  ): Promise<void> {
    const key = `chapter_progress:${sessionId}`;
    const progress = {
      userId,
      book,
      chapter,
      chunksRead,
      totalChunks,
      lastUpdated: new Date().toISOString()
    };

    try {
      await redisConnection.setex(key, 60 * 60 * 2, JSON.stringify(progress)); // 2 hours
      logger.info({ userId, sessionId, book, chapter, chunksRead, totalChunks }, 'Stored chapter progress');
    } catch (error) {
      logger.error({ error, userId, sessionId }, 'Failed to store chapter progress');
    }
  }

  /**
   * Get chapter reading progress
   */
  public static async getChapterProgress(sessionId: string): Promise<{
    userId: string,
    book: string,
    chapter: number,
    chunksRead: number,
    totalChunks: number,
    lastUpdated: string
  } | null> {
    const key = `chapter_progress:${sessionId}`;

    try {
      const progressStr = await redisConnection.get(key);
      return progressStr ? JSON.parse(progressStr) : null;
    } catch (error) {
      logger.error({ error, sessionId }, 'Failed to get chapter progress');
      return null;
    }
  }

  /**
   * Clear all memory for testing purposes
   */
  public static async clearAllMemory(): Promise<void> {
    if (process.env.NODE_ENV === 'test') {
      await redisConnection.flushall();
    }
  }
}