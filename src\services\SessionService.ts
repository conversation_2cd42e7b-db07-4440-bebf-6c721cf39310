// src/services/SessionService.ts
import z from 'zod';
import { logger } from '../config/logger.config';
import { pool } from '../database/client';
import { redisConnection } from '../queue/connection';
import { StudySession, StudySessionSchema, SessionTypeSchema, SessionStatusSchema } from '../types';

type SessionType = z.infer<typeof SessionTypeSchema>;
type SessionStatus = z.infer<typeof SessionStatusSchema>;

interface StartSessionOptions {
  type: SessionType;
  step?: string;
  context?: Record<string, any>;
  expiresInMinutes?: number;
}

export class SessionService {
  /**
   * Creates a new session for a user, ensuring any previous active sessions are closed.
   */
  public static async startSession(userId: string, options: StartSessionOptions): Promise<StudySession> {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Step 1: Expire any existing active sessions for this user to prevent conflicts.
      await client.query(
        "UPDATE study_sessions SET status = 'EXPIRED' WHERE user_id = $1 AND status = 'ACTIVE'",
        [userId]
      );

      // Step 2: Create the new session.
      const { type, step, context, expiresInMinutes } = options;
      const expiresAt = expiresInMinutes ? `NOW() + INTERVAL '${expiresInMinutes} minutes'` : 'NULL';
      
      const insertQuery = `
        INSERT INTO study_sessions (user_id, session_type, session_step, session_context, expires_at)
        VALUES ($1, $2, $3, $4, ${expiresAt})
        RETURNING *;
      `;
      
      const { rows } = await client.query(insertQuery, [userId, type, step, context]);
      
      await client.query('COMMIT');
      
      const session = StudySessionSchema.parse(rows[0]);
      // Cache the active session id for quick lookup (TTL 2 hours)
      await redisConnection.setex(`active_session:${userId}`, 7200, session.id);
      logger.info({ userId, sessionId: session.id, type: session.session_type }, 'Started new session');
      return session;

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error({ userId, error }, 'Failed to start session; transaction rolled back.');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Creates a new Bible study session with proper initial context
   */
  public static async startBibleStudySession(userId: string, book: string): Promise<StudySession> {
    return this.startSession(userId, {
      type: 'BIBLE_STUDY',
      step: 'SENDING_SCENE',
      context: { 
        book,
        currentStep: 'SENDING_SCENE',
        previousResponses: [],
        currentPassage: null
      },
      expiresInMinutes: 60
    });
  }

  /**
   * Finds the single currently active session for a user, if one exists.
   */
  public static async findActiveSession(userId: string): Promise<StudySession | null> {
    // Try redis first
    const cachedId = await redisConnection.get(`active_session:${userId}`);
    if (cachedId) {
      const { rows } = await pool.query('SELECT * FROM study_sessions WHERE id = $1', [cachedId]);
      if (rows.length) return StudySessionSchema.parse(rows[0]);
      // fall-through if not found in DB
    }

    const query = "SELECT * FROM study_sessions WHERE user_id = $1 AND status = 'ACTIVE' LIMIT 1";
    const { rows } = await pool.query(query, [userId]);
    if (rows.length === 0) return null;

    // Cache result for next time
    await redisConnection.setex(`active_session:${userId}`, 7200, rows[0].id);
    return StudySessionSchema.parse(rows[0]);
  }

  /**
   * Updates the step of an ongoing session.
   */
  public static async updateSessionStep(sessionId: string, newStep: string): Promise<void> {
    const query = "UPDATE study_sessions SET session_step = $1, updated_at = NOW() WHERE id = $2";
    await pool.query(query, [newStep, sessionId]);
    logger.info({ sessionId, newStep }, 'Updated session step');
  }

  /**
   * Updates the context of an ongoing session.
   */
  public static async updateSessionContext(sessionId: string, context: Record<string, any>): Promise<void> {
    const query = "UPDATE study_sessions SET session_context = $1, updated_at = NOW() WHERE id = $2";
    await pool.query(query, [context, sessionId]);
    logger.info({ sessionId }, 'Updated session context');
  }

  /**
   * Updates both step and context of a session in one operation.
   */
  public static async updateSession(sessionId: string, newStep: string, context: Record<string, any>): Promise<void> {
    const query = "UPDATE study_sessions SET session_step = $1, session_context = $2, updated_at = NOW() WHERE id = $3";
    await pool.query(query, [newStep, context, sessionId]);
    logger.info({ sessionId, newStep }, 'Updated session step and context');
  }

  /**
   * Updates the study mode of an ongoing session.
   */
  public static async updateStudyMode(sessionId: string, studyMode: string): Promise<void> {
    const query = "UPDATE study_sessions SET study_mode = $1, updated_at = NOW() WHERE id = $2";
    await pool.query(query, [studyMode, sessionId]);
    logger.info({ sessionId, studyMode }, 'Updated session study mode');
  }

  /**
   * Ends a session by setting its status to COMPLETED or EXPIRED.
   */
  public static async endSession(sessionId: string, finalStatus: 'COMPLETED' | 'EXPIRED'): Promise<void> {
    // Fetch user id to clear cache
    const { rows } = await pool.query('UPDATE study_sessions SET status = $1, updated_at = NOW() WHERE id = $2 RETURNING user_id', [finalStatus, sessionId]);
    if (rows.length) {
      await redisConnection.del(`active_session:${rows[0].user_id}`);
    }
    logger.info({ sessionId, finalStatus }, 'Session ended');
  }

  /**
   * Expires all sessions that have passed their expiration date
   */
  public static async expireStaleSessions(): Promise<void> {
    const query = `
      UPDATE study_sessions
      SET status = 'EXPIRED', updated_at = NOW()
      WHERE status = 'ACTIVE' AND expires_at IS NOT NULL AND expires_at <= NOW()
      RETURNING id;
    `;
    const { rows } = await pool.query(query);
    if (rows.length > 0) {
      for (const r of rows) {
        await redisConnection.del(`active_session:${r.id}`);
      }
      logger.info({ count: rows.length }, 'Expired stale sessions');
    }
  }
}