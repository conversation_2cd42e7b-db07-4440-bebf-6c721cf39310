// src/services/SessionHandlerV2.ts
import { SessionHandler } from './SessionHandler';
import { AIService } from './AIService';
import { SessionService } from './SessionService';
import { logger } from '../config/logger.config';
import { messageQueue } from '../queue/messageQueue';
import { StudySession, User } from '../types';
import { parseChoice } from '../utils/ChoiceParser';
import { ensureMinTypingDelay } from '../utils/messagePacer';

async function send(toJid: string, message: string) {
  // Ensure typing indicator is shown before sending message
  await messageQueue.add('send-presence', { toJid, presence: 'composing' });
  
  // Calculate a natural delay based on message length (simulate human typing)
  const typingDelay = Math.min(2000, 500 + message.length * 10); 
  const startTime = Date.now();
  
  // Wait for minimum typing delay to ensure typing indicator is visible
  await ensureMinTypingDelay(startTime, typingDelay);
  
  // Send the message
  await messageQueue.add('send-text', { toJid, text: message });
  
  // Send "paused" presence after message is sent
  await messageQueue.add('send-presence', { toJid, presence: 'paused' });
}

/**
 * V2 Bible-Study flow with hub-and-spoke choices. Non-BibleStudy sessions delegate to original SessionHandler.
 */
export class SessionHandlerV2 {
  /** Entry point mirrors SessionHandler.handle */
  public static async handle(session: StudySession, user: User, message: string) {
    if (session.session_type !== 'BIBLE_STUDY') {
      return SessionHandler.handle(session, user, message);
    }

    return this.handleBibleStudyMessage(session, user, message, user.jid!);
  }

  private static async handleBibleStudyMessage(session: StudySession, user: User, message: string, userJid: string): Promise<void> {
    const step = session.session_step || 'SENDING_SCENE';
    const context = session.session_context || {};
    const book = user.current_book || 'John';

    switch (step) {
      case 'PRESENTING_CHOICES': {
        // We arrive here right after we asked the application follow-up in old flow
        const lastApplication = context.lastApplication || '';
        const passage = context.currentPassage || 'this passage';
        const choices = await AIService.generateFollowUpChoices(passage, lastApplication);

        // Persist choices in session_context for later validation
        await SessionService.updateSessionContext(session.id, { ...context, lastChoices: choices });
        await send(userJid, `What feels right for you next?\n${choices.join('\n')}`);
        await SessionService.updateSessionStep(session.id, 'AWAITING_USER_CHOICE');
        break;
      }

      case 'AWAITING_USER_CHOICE': {
        // Parse numeric choice
        const choices: string[] = context.lastChoices || [];
        const selected = parseChoice(message, choices.length);
        if (!selected) {
          await send(userJid, 'I didn\'t catch that. Could you reply with the number of your choice?');
          return;
        }

        switch (selected) {
          case 1: // Continue to next passage
            await send(userJid, 'Great, let\'s continue.');
            await SessionService.updateSession(session.id, 'SENDING_VERSES', context);
            // Recurse into traditional study flow
            return SessionHandler.handle({ ...session, session_step: 'SENDING_VERSES', session_context: context }, user, '');
          case 2: { // Explore related verses based on user insight
             const query = context.lastApplication || context.lastInterpretation || message;
             // Use RAGService to fetch related verses
             const { RAGService } = await import('./RAGService');
             const verses = await RAGService.search(query);
             if (!verses.length) {
               await send(userJid, 'I couldn\'t find related verses right now. Let\'s continue our study.');
               await SessionService.updateSession(session.id, 'SENDING_VERSES', context);
               return SessionHandler.handle({ ...session, session_step: 'SENDING_VERSES', session_context: context }, user, '');
             }

             const topVerses = verses.slice(0, 3);
             const verseText = topVerses.map(v => `${v.book} ${v.chapter}:${v.verse} – ${v.text}`).join('\n');
             await send(userJid, `Here are some related verses you might find helpful:\n${verseText}`);
             // Ask observation question about first verse
             const reflection = await AIService.generateReflectionQuestion(topVerses);
             await send(userJid, reflection);
             await SessionService.updateSession(session.id, 'AWAITING_OBSERVATION', { ...context, currentPassage: `${topVerses[0].book} ${topVerses[0].chapter}:${topVerses[0].verse}`, currentVerses: topVerses });
             return;
          }
          case 3: { // Deeper personal application
             const passageRef = context.currentPassage || 'this passage';
             const userInterp = context.lastInterpretation || context.lastObservation || message;
             const q = await AIService.generateApplicationQuestion(passageRef, userInterp);
             await send(userJid, q);
             await SessionService.updateSessionStep(session.id, 'AWAITING_APPLICATION');
             return; // Wait for user response which will continue in original handler
          }
          default:
            await send(userJid, 'Sorry, I can\'t process that choice.');
            break;
        }
        break;
      }

      // Default: delegate back to original handler for existing states.
      default: {
        // Call original SessionHandler logic
        await SessionHandler.handle(session, user, message);

        // After original handler processes, intercept if it just moved from AWAITING_APPLICATION to SENDING_VERSES.
        // We detect this by checking if the step became 'SENDING_VERSES' and context.lastApplication was set.
        const updatedSession = await SessionService.findActiveSession(user.id);
        if (updatedSession && updatedSession.session_step === 'SENDING_VERSES' && (updatedSession.session_context as any)?.lastApplication) {
          // Override to present hub choices instead of auto-continue, but only if flag enabled.
          logger.info({ sessionId: updatedSession.id }, 'Switching to PRESENTING_CHOICES in v2 flow');
          await SessionService.updateSessionStep(updatedSession.id, 'PRESENTING_CHOICES');
          await this.handleBibleStudyMessage({ ...updatedSession, session_step: 'PRESENTING_CHOICES' }, user, message, userJid);
        }
      }
    }
  }
} 