// src/database/client.ts
import { Pool } from 'pg';
import { validateEnv } from '../config/env.config';

const env = validateEnv();

export const pool = new Pool({
  connectionString: env.DATABASE_URL,
});

// Function to test the database connection
export async function testDbConnection() {
  try {
    const client = await pool.connect();
    console.log('✅ PostgreSQL database connected successfully!');
    client.release();
  } catch (error) {
    console.error('❌ Failed to connect to the PostgreSQL database:', error);
    process.exit(1);
  }
}