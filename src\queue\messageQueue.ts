// src/queue/messageQueue.ts
import { Queue } from 'bullmq';
import { redisConnection } from './connection';

// Create a queue named 'message-queue'
export const messageQueue = new Queue('message-queue', {
  connection: redisConnection,
  defaultJobOptions: {
    attempts: 3, // Retry a failed job up to 3 times
    backoff: {
      type: 'exponential',
      delay: 5000, // 5s, 10s, 20s
    }
  }
});