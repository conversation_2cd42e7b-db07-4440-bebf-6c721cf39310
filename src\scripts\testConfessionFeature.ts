// src/scripts/testConfessionFeature.ts
// Comprehensive test script for confession feature

import { config } from 'dotenv';
import pino from 'pino';
import { StateManager } from '../services/StateManager';
import { AIService } from '../services/AIService';
import { ConfessionUtils } from '../utils/ConfessionUtils';
import { SessionService } from '../services/SessionService';
import { ConfessionHandler } from '../services/ConfessionHandler';
import { User } from '../types';

// Load environment variables
config();

const logger = pino({ transport: { target: 'pino-pretty' } });

async function testConfessionFeature() {
  logger.info('🧪 Starting confession feature validation tests...');

  try {
    // Test 1: Confession utilities (no external dependencies)
    await testConfessionUtils();

    // Test 2: Database operations (requires DB connection)
    if (process.env.DATABASE_URL) {
      await testDatabaseOperations();
    } else {
      logger.warn('Skipping database tests - DATABASE_URL not set');
    }

    // Test 3: AI refinement service (requires GROQ API)
    if (process.env.GROQ_API_KEY) {
      await testAIRefinement();
    } else {
      logger.warn('Skipping AI tests - GROQ_API_KEY not set');
    }

    // Test 4: Session handling (requires DB)
    if (process.env.DATABASE_URL && process.env.REDIS_URL) {
      await testSessionHandling();
    } else {
      logger.warn('Skipping session tests - DATABASE_URL or REDIS_URL not set');
    }

    // Test 5: Reminder system (utility functions only)
    await testReminderSystem();

    logger.info('✅ All available confession feature tests passed!');

  } catch (error) {
    logger.error({ error }, '❌ Confession feature tests failed');
    throw error;
  }
}

async function testDatabaseOperations() {
  logger.info('Testing database operations...');
  
  // Create a test user
  const testUser = await StateManager.findOrCreateUser('test-confession-user', '<EMAIL>');
  
  // Test saving a confession
  const originalText = "I have been struggling with patience lately and need to work on being more understanding.";
  const refinedText = "I have been struggling with patience lately and recognize the need to cultivate greater understanding and compassion in my daily interactions.";
  
  const confession = await StateManager.saveConfession(
    testUser.id,
    originalText,
    refinedText,
    '09:00',
    'once'
  );
  
  logger.info({ confessionId: confession.id }, 'Successfully saved confession');
  
  // Test retrieving confession
  const retrievedConfession = await StateManager.getUserConfession(testUser.id);
  if (!retrievedConfession || retrievedConfession.id !== confession.id) {
    throw new Error('Failed to retrieve saved confession');
  }
  
  logger.info('Successfully retrieved confession');
  
  // Test updating confession
  const updatedOriginal = "I have been struggling with patience and also with forgiveness.";
  const updatedRefined = "I have been struggling with patience and forgiveness, recognizing my need for growth in these areas.";
  
  await StateManager.updateConfession(confession.id, updatedOriginal, updatedRefined);
  logger.info('Successfully updated confession');
  
  // Test scheduling reminder
  const reminderTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // Tomorrow
  await StateManager.scheduleConfessionReminder(confession.id, reminderTime);
  logger.info('Successfully scheduled confession reminder');
  
  // Test getting confessions due for reminder
  const confessionsDue = await StateManager.getConfessionsDueForReminder(new Date(Date.now() + 25 * 60 * 60 * 1000));
  if (confessionsDue.length === 0) {
    logger.warn('No confessions found due for reminder (this might be expected)');
  } else {
    logger.info({ count: confessionsDue.length }, 'Found confessions due for reminder');
  }
  
  // Clean up test data
  await StateManager.markConfessionReminderSent(confession.id);
  logger.info('Database operations test completed');
}

async function testAIRefinement() {
  logger.info('Testing AI refinement service...');
  
  const testConfessions = [
    "i been angry alot lately and dont know what to do about it",
    "I struggle with pride and thinking I'm better than others sometimes",
    "forgiveness is hard for me especially when people hurt me repeatedly"
  ];
  
  for (const confession of testConfessions) {
    try {
      const refined = await AIService.refineConfession(confession);
      
      if (!refined || refined.length === 0) {
        throw new Error('AI refinement returned empty result');
      }
      
      logger.info({
        original: confession,
        refined: refined
      }, 'AI refinement successful');
      
    } catch (error) {
      logger.error({ error, confession }, 'AI refinement failed');
      throw error;
    }
  }
  
  logger.info('AI refinement service test completed');
}

async function testConfessionUtils() {
  logger.info('Testing confession utilities...');
  
  // Test time parsing
  const timeInputs = [
    'tomorrow at 7am',
    'every morning at 8:30am',
    'once at noon',
    'invalid time input'
  ];
  
  for (const input of timeInputs) {
    const parsed = ConfessionUtils.parseReminderTime(input, 'UTC');
    logger.info({
      input,
      parsed: parsed?.toISO() || 'null'
    }, 'Time parsing result');
  }
  
  // Test text validation
  const textInputs = [
    '', // Empty
    'short', // Too short
    'This is a valid confession text that should pass validation',
    'x'.repeat(2500) // Too long
  ];
  
  for (const text of textInputs) {
    const validation = ConfessionUtils.validateConfessionText(text);
    logger.info({
      textLength: text.length,
      isValid: validation.isValid,
      message: validation.message
    }, 'Text validation result');
  }
  
  // Test frequency extraction
  const frequencyInputs = [
    'every day at 7am',
    'weekly on Sunday',
    'once tomorrow',
    'every month at noon'
  ];
  
  for (const input of frequencyInputs) {
    const frequency = ConfessionUtils.extractReminderFrequency(input);
    logger.info({
      input,
      frequency
    }, 'Frequency extraction result');
  }
  
  // Test message formatting
  const reminderMessage = ConfessionUtils.formatConfessionReminder(
    'John',
    'I need to work on my patience and understanding.'
  );
  
  logger.info({ reminderMessage }, 'Reminder message formatting test');
  
  logger.info('Confession utilities test completed');
}

async function testSessionHandling() {
  logger.info('Testing session handling...');
  
  // Create a test user
  const testUser = await StateManager.findOrCreateUser('test-session-user', '<EMAIL>');
  
  // Test starting a confession session
  const session = await SessionService.startSession(testUser.id, {
    type: 'BIBLE_STUDY',
    step: 'CONFESSION_PENDING_INPUT',
    context: { isOnboarding: false }
  });
  
  logger.info({ sessionId: session.id }, 'Successfully started confession session');
  
  // Note: Full session flow testing would require mocking the message queue
  // and user interactions, which is beyond the scope of this validation script
  
  // Clean up
  await SessionService.endSession(session.id, 'COMPLETED');
  logger.info('Session handling test completed');
}

async function testReminderSystem() {
  logger.info('Testing reminder system...');
  
  // Test reminder message formatting
  const testUser: Pick<User, 'id' | 'jid' | 'name' | 'user_timezone'> = {
    id: 'test-user-id',
    jid: '<EMAIL>',
    name: 'Test User',
    user_timezone: 'UTC'
  };
  
  const testConfession = {
    id: 'test-confession-id',
    user_id: testUser.id,
    original_text: 'Original confession text',
    refined_text: 'I need to work on my patience and show more understanding to others.',
    reminder_time: '09:00',
    reminder_frequency: 'once',
    next_reminder_at: new Date(),
    is_active: true,
    created_at: new Date(),
    updated_at: new Date()
  };
  
  const reminderMessage = ConfessionUtils.formatConfessionReminder(
    testUser.name,
    testConfession.refined_text
  );
  
  logger.info({ reminderMessage }, 'Reminder message test');
  
  // Test frequency calculations
  const frequencies = ['daily', 'weekly', 'monthly', 'once'];

  for (const freq of frequencies) {
    logger.info({
      frequency: freq,
      description: ConfessionUtils.formatFrequencyDisplay(freq)
    }, 'Frequency display formatting');
  }
  
  logger.info('Reminder system test completed');
}

// Run the tests if this script is executed directly
if (require.main === module) {
  testConfessionFeature()
    .then(() => {
      logger.info('All tests completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error({ error }, 'Tests failed');
      process.exit(1);
    });
}

export { testConfessionFeature };
