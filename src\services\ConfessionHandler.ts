// src/services/ConfessionHandler.ts
import { logger } from '../config/logger.config';
import { StudySession, User, ConfessionContext } from '../types';
import { StateManager } from './StateManager';
import { SessionService } from './SessionService';
import { AIService } from './AIService';
import { messageQueue } from '../queue/messageQueue';
import { ensureMinTypingDelay } from '../utils/messagePacer';
import { ConfessionUtils } from '../utils/ConfessionUtils';
import * as chrono from 'chrono-node';
import { DateTime } from 'luxon';

// Helper function to send messages with typing indicator
async function send(toJid: string, message: string): Promise<void> {
  // Show typing indicator
  await messageQueue.add('send-presence', { toJid, presence: 'composing' });
  
  // Calculate a natural delay based on message length
  const typingDelay = Math.min(2500, 800 + message.length * 12);
  const startTime = Date.now();
  
  // Ensure typing indicator is visible for a realistic time
  await ensureMinTypingDelay(startTime, typingDelay);
  
  // Send the message
  await messageQueue.add('send-text', { toJid, text: message });
  
  // Stop typing indicator
  await messageQueue.add('send-presence', { toJid, presence: 'paused' });
}

export class ConfessionHandler {
  /**
   * Handle confession-related session steps
   */
  public static async handle(session: StudySession, user: User, message: string) {
    const userJid = user.jid!;
    const step = session.session_step;
    const context = session.session_context as ConfessionContext;

    logger.info({ 
      sessionId: session.id, 
      userId: user.id, 
      step, 
      isOnboarding: context?.isOnboarding 
    }, 'Handling confession session step');

    switch (step) {
      case 'CONFESSION_PENDING_INPUT':
        return this.handleConfessionInput(session, user, message, userJid);
      
      case 'CONFESSION_AWAITING_CONFIRMATION':
        return this.handleConfessionConfirmation(session, user, message, userJid);
      
      case 'CONFESSION_AWAITING_REMINDER_TIME':
        return this.handleReminderTimeInput(session, user, message, userJid);
      
      case 'CONFESSION_EDIT_PENDING_INPUT':
        return this.handleEditInput(session, user, message, userJid);
      
      case 'CONFESSION_EDIT_AWAITING_CONFIRMATION':
        return this.handleEditConfirmation(session, user, message, userJid);
      
      default:
        logger.warn({ step }, 'Unknown confession session step');
        await send(userJid, "I'm not sure what happened there. Let me help you get back on track.");
        await SessionService.endSession(session.id, 'COMPLETED');
    }
  }

  /**
   * Handle initial confession input
   */
  private static async handleConfessionInput(
    session: StudySession, 
    user: User, 
    message: string, 
    userJid: string
  ): Promise<void> {
    const originalText = message.trim();
    
    if (!originalText || originalText.length < 10) {
      await send(userJid, "Please share a bit more about what you'd like to confess or reflect on. Take your time to express your thoughts.");
      return;
    }

    try {
      // Refine the confession using AI
      await send(userJid, "Thank you for sharing. Let me help you refine this...");
      
      const refinedText = await AIService.refineConfession(originalText);
      
      // Update session context with both texts
      const updatedContext: ConfessionContext = {
        ...session.session_context,
        originalText,
        refinedText
      };
      
      await SessionService.updateSessionContext(session.id, updatedContext);
      
      // Present the refined version for confirmation
      const confirmationMessage = `Thank you for sharing, ${user.name}. I've refined it slightly to help you articulate it clearly. Does this capture what you wanted to express?\n\n"${refinedText}"\n\nType "yes" if this looks good, or "no" if you'd like to re-enter your confession.`;
      
      await send(userJid, confirmationMessage);
      await SessionService.updateSessionStep(session.id, 'CONFESSION_AWAITING_CONFIRMATION');
      
    } catch (error) {
      logger.error({ error, userId: user.id }, 'Error processing confession input');
      await send(userJid, "I'm sorry, there was an issue processing your confession. Please try again.");
    }
  }

  /**
   * Handle confession confirmation
   */
  private static async handleConfessionConfirmation(
    session: StudySession, 
    user: User, 
    message: string, 
    userJid: string
  ): Promise<void> {
    const response = message.trim().toLowerCase();
    const context = session.session_context as ConfessionContext;
    
    if (response === 'yes' || response === 'y') {
      // User approved the refined confession
      await this.proceedToReminderSetup(session, user, userJid);
      
    } else if (response === 'no' || response === 'n') {
      // User wants to re-enter
      await send(userJid, "No problem at all. Please re-enter your confession, and we can try refining it again. Take your time.");
      await SessionService.updateSessionStep(session.id, 'CONFESSION_PENDING_INPUT');
      
    } else {
      // Invalid response
      await send(userJid, `Please type "yes" if the refined confession looks good, or "no" if you'd like to re-enter it.`);
    }
  }

  /**
   * Proceed to reminder setup
   */
  private static async proceedToReminderSetup(
    session: StudySession, 
    user: User, 
    userJid: string
  ): Promise<void> {
    const reminderMessage = `Okay, ${user.name}. Now, what time would you like me to gently remind you of this confession? (e.g., 'every morning at 7am', 'every Sunday at 10pm', 'just once tomorrow at noon').`;
    
    await send(userJid, reminderMessage);
    await SessionService.updateSessionStep(session.id, 'CONFESSION_AWAITING_REMINDER_TIME');
  }

  /**
   * Handle reminder time input
   */
  private static async handleReminderTimeInput(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const timeInput = message.trim();
    const context = session.session_context as ConfessionContext;

    try {
      // Parse the time input
      const parsedTime = chrono.parseDate(timeInput);

      if (!parsedTime) {
        await send(userJid, "I couldn't understand that time. Please try again with something like '7am tomorrow', 'every Sunday at 10pm', or 'once at noon'.");
        return;
      }

      // Extract reminder frequency from user input
      const frequency = ConfessionUtils.extractReminderFrequency(timeInput);

      // Use user's timezone or fallback to UTC if null
      const userTimezone = user.user_timezone || 'UTC';

      // Improved time parsing: Create time in user's timezone directly
      const timeString = parsedTime.toTimeString().split(' ')[0].substring(0, 5); // HH:MM format
      const [hours, minutes] = timeString.split(':').map(Number);

      // Create DateTime in user's timezone with today's date and parsed time
      let reminderTime = DateTime.now()
        .setZone(userTimezone)
        .set({ hour: hours, minute: minutes, second: 0, millisecond: 0 });

      // Get current time in user's timezone for comparison
      const nowInUserTz = DateTime.now().setZone(userTimezone);

      // If the time has already passed today, schedule for tomorrow
      if (reminderTime < nowInUserTz) {
        reminderTime = reminderTime.plus({ days: 1 });
      }

      // Log for debugging timezone issues
      logger.info({
        userId: user.id,
        userInput: timeInput,
        parsedTimeString: timeString,
        userTimezone,
        reminderTimeInUserTz: reminderTime.toFormat('yyyy-MM-dd HH:mm:ss'),
        reminderTimeUTC: reminderTime.toUTC().toFormat('yyyy-MM-dd HH:mm:ss')
      }, 'Confession reminder time parsing details');

      // CRITICAL FIX: Deactivate any existing active confession reminders for this user
      await StateManager.deactivateUserConfessionReminders(user.id);

      // Save the confession to database
      const confession = await StateManager.saveConfession(
        user.id,
        context.originalText!,
        context.refinedText!,
        timeString, // Use the corrected time string
        frequency
      );

      // Convert to UTC for database storage
      const nextReminderAt = reminderTime.toUTC().toJSDate();
      await StateManager.scheduleConfessionReminder(confession.id, nextReminderAt);

      // Confirm to user with time in their timezone
      const userTimeString = reminderTime.toFormat('h:mm a');
      const confirmationMessage = `Got it, ${user.name}! I'll gently remind you about your confession at ${userTimeString}. You can also ask me to 'show confessions' anytime.`;
      await send(userJid, confirmationMessage);

      // Complete the confession flow
      await this.completeConfessionFlow(session, user, userJid);

    } catch (error) {
      logger.error({ error, userId: user.id }, 'Error processing reminder time');
      await send(userJid, "I'm sorry, there was an issue setting up your reminder. Please try again with a time like '7am tomorrow' or 'Sunday at 10pm'.");
    }
  }

  /**
   * Complete the confession flow
   */
  private static async completeConfessionFlow(
    session: StudySession, 
    user: User, 
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ConfessionContext;
    
    if (context.isOnboarding) {
      // If this was during onboarding, complete the onboarding process (Bible book was already selected)
      await send(userJid, `Your confession has been saved and you're all set up! Feel free to simply type 'start study' whenever you're ready to dive in, or ask me any questions about God's Word. I'm excited for what we'll discover together!`);
      await StateManager.updateOnboardingStep(user.id, 'ONBOARDING_COMPLETE');
    } else {
      // If this was a standalone confession, return to Q&A mode
      await send(userJid, `Your confession has been saved. Feel free to continue with your Bible study or ask me any questions about God's Word.`);
    }
    
    // End the confession session
    await SessionService.endSession(session.id, 'COMPLETED');
  }

  /**
   * Handle edit confession input
   */
  private static async handleEditInput(
    session: StudySession, 
    user: User, 
    message: string, 
    userJid: string
  ): Promise<void> {
    const originalText = message.trim();
    
    if (!originalText || originalText.length < 10) {
      await send(userJid, "Please share your updated confession. Take your time to express your thoughts.");
      return;
    }

    try {
      // Refine the updated confession using AI
      await send(userJid, "Thank you for the update. Let me help you refine this...");
      
      const refinedText = await AIService.refineConfession(originalText);
      
      // Update session context with both texts
      const updatedContext: ConfessionContext = {
        ...session.session_context,
        originalText,
        refinedText
      };
      
      await SessionService.updateSessionContext(session.id, updatedContext);
      
      // Present the refined version for confirmation
      const confirmationMessage = `Thank you for sharing, ${user.name}. I've refined this new version. Does this capture what you wanted to express?\n\n"${refinedText}"\n\nType "yes" to save this updated version, or "no" to keep your original confession.`;
      
      await send(userJid, confirmationMessage);
      await SessionService.updateSessionStep(session.id, 'CONFESSION_EDIT_AWAITING_CONFIRMATION');
      
    } catch (error) {
      logger.error({ error, userId: user.id }, 'Error processing confession edit');
      await send(userJid, "I'm sorry, there was an issue processing your updated confession. Please try again.");
    }
  }

  /**
   * Handle edit confirmation
   */
  private static async handleEditConfirmation(
    session: StudySession, 
    user: User, 
    message: string, 
    userJid: string
  ): Promise<void> {
    const response = message.trim().toLowerCase();
    const context = session.session_context as ConfessionContext;
    
    if (response === 'yes' || response === 'y') {
      // User approved the updated confession
      try {
        if (context.editingConfessionId) {
          await StateManager.updateConfession(
            context.editingConfessionId,
            context.originalText!,
            context.refinedText!
          );
          
          await send(userJid, `Your confession has been updated successfully, ${user.name}.`);
        }
      } catch (error) {
        logger.error({ error, userId: user.id }, 'Error updating confession');
        await send(userJid, "I'm sorry, there was an issue updating your confession.");
      }
      
    } else if (response === 'no' || response === 'n') {
      // User wants to keep original - save what they sent without refinement
      try {
        if (context.editingConfessionId) {
          await StateManager.updateConfession(
            context.editingConfessionId,
            context.originalText!,
            context.originalText! // Use original text as both original and refined
          );
          
          await send(userJid, `I'll just save what you sent, ${user.name}. Your confession has been updated.`);
        }
      } catch (error) {
        logger.error({ error, userId: user.id }, 'Error saving original confession');
        await send(userJid, "I'm sorry, there was an issue saving your confession.");
      }
      
    } else {
      // Invalid response
      await send(userJid, `Please type "yes" to save the refined version, or "no" to save your original text.`);
      return;
    }
    
    // End the edit session
    await SessionService.endSession(session.id, 'COMPLETED');
  }
}
