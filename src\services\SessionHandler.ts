// src/services/SessionHandler.ts
import { logger } from '../config/logger.config';
import { messageQueue } from '../queue/messageQueue';
import { StudySession, User } from '../types';
import { SessionService } from './SessionService';
import { StateManager } from './StateManager';
import { AIService } from './AIService';
import { sendPaced, ensureMinTypingDelay } from '../utils/messagePacer';
import { parseChoice } from '../utils/ChoiceParser';
import { MemoryService } from './MemoryService';
import { VoiceService } from './VoiceService';

// Thin wrapper so state-machine code remains clean. Uses paced delivery by default.
async function send(toJid: string, message: string, split = true) {
  // Ensure typing indicator is shown before sending message
  await messageQueue.add('send-presence', { toJid, presence: 'composing' });
  
  // Calculate a natural delay based on message length (simulate human typing)
  const typingDelay = Math.min(2000, 500 + message.length * 10); 
  const startTime = Date.now();
  
  // Wait for minimum typing delay to ensure typing indicator is visible
  await ensureMinTypingDelay(startTime, typingDelay);
  
  // Send the message with proper pacing between chunks
  await sendPaced(toJid, message, { split, delayMs: 2000 });
  
  // Send "paused" presence after message is sent
  await messageQueue.add('send-presence', { toJid, presence: 'paused' });
}

export class SessionHandler {
  public static async handle(session: StudySession, user: User, message: string) {
    const userJid = user.jid!;
    switch (session.session_type) {
      case 'BIBLE_STUDY':
        await this.handleBibleStudyMessage(session, user, message, userJid);
        break;
      case 'CHECK_IN':
        await this.handleCheckIn(session, user, message, userJid);
        break;
      case 'REMINDER':
        await this.handleReminder(session, user, message, userJid);
        break;
    }
  }

  private static async handleCheckIn(session: StudySession, user: User, message: string, userJid: string): Promise<void> {
    // This gets called in two scenarios:
    // 1. When the session is first created (message is empty)
    // 2. When the user replies to the check-in
    
    // Case 1: Initial check-in message
    if (!message || message.trim() === '') {
      const userName = user.name || 'there';
      const prompt = `Hey ${userName}, how are you feeling today?`;
      await send(userJid, prompt);
      return; // Keep session active to wait for response
    }
    
    // Case 2: User responded to check-in
    try {
      // Generate empathetic response for voice note
      const aiResponse = await AIService.generateEmpathyResponse(message, user.name || 'friend');
      
      // Generate and send voice note
      try {
        logger.info({ userId: user.id }, 'Generating voice note response');
        const audioBuffer = await VoiceService.textToSpeech(aiResponse);
        
        if (audioBuffer) {
          const audioUrl = await VoiceService.uploadAudioToR2(audioBuffer, user.id);
          
          // Send audio message via queue
          await messageQueue.add('send-audio', { 
            toJid: userJid, 
            audioUrl 
          });
          
          logger.info({ userId: user.id }, 'Voice note queued for delivery');
        }
      } catch (error) {
        logger.error({ error, userId: user.id }, 'Failed to generate or send voice note');
        const cleanText = aiResponse.replace(/^"+|"+$/g, '');
        await send(userJid, cleanText);
        logger.warn({ userId: user.id }, 'TTS failed – sent text fallback');
      }
      
      // End session and return to Q&A mode immediately after sending response
      await SessionService.endSession(session.id, 'COMPLETED');
    } catch (error) {
      logger.error({ error, userId: user.id }, 'Error handling check-in');
      await SessionService.endSession(session.id, 'COMPLETED');
    }
  }

  private static async handleReminder(session: StudySession, user: User, message: string, userJid: string): Promise<void> {
    const userName = user.name || 'there';
    const reminderText = `Hi ${userName}! It's time for your daily Bible study. Type "start study" to begin.`;
    await send(userJid, reminderText);
    
    // Reminders are one-way notifications, end session immediately
    await SessionService.endSession(session.id, 'COMPLETED');
  }

  private static async handleBibleStudyMessage(session: StudySession, user: User, message: string, userJid: string): Promise<void> {
    const step = session.session_step || 'SENDING_SCENE'; // Default to start
    const book = user.current_book!; // We know this exists for a study session
    const context = session.session_context || {};

    switch (step) {
      case 'SENDING_SCENE': {
        const sceneSetter = await AIService.generateSceneSetter(book);
        await send(userJid, sceneSetter);
        await SessionService.updateSessionStep(session.id, 'SENDING_VERSES');
        // proceed to send verses immediately without recursion
        return this.handleBibleStudyMessage({ ...session, session_step: 'SENDING_VERSES' }, user, message, userJid);
      }

      case 'SENDING_VERSES': {
        const verseBatchSize = parseInt(process.env.VERSE_BATCH_SIZE || '3', 10);
        
        const verses = await StateManager.getNextStudyBlock(
          user.id,
          book,
          user.last_completed_chapter ?? 0,
          user.last_completed_verse ?? 0,
          verseBatchSize
        );
        
        if (verses.length === 0) {
          await send(userJid, `Congratulations! You have completed the book of ${book}!`);
          await SessionService.endSession(session.id, 'COMPLETED');
          return;
        }
        
        const verseText = verses
          .map(v => `[${v.chapter}:${v.verse}] ${v.text}`)
          .join('\n\n');
        
        const passageRef = `${book} ${verses[0].chapter}:${verses[0].verse}-${verses[verses.length - 1].verse}`;
        
        // Update session context with current passage info
        const updatedContext = {
          ...context,
          currentPassage: passageRef,
          currentVerses: verses.map(v => ({ chapter: v.chapter, verse: v.verse, text: v.text }))
        };
        
        await SessionService.updateSessionContext(session.id, updatedContext);
        
        const reflection = await AIService.generateReflectionQuestion(verses);
        // New flow default: send passage & question in one paced bubble
        await send(
          userJid,
          `Okay, let's look at ${passageRef}.\n\n${verseText}\n\n${reflection}`
        );
        await SessionService.updateSessionStep(session.id, 'AWAITING_OBSERVATION');
        break;
      }

      case 'AWAITING_OBSERVATION': {
        // Store the user's observation response
        await MemoryService.addBibleStudyResponse(
          user.id,
          session.id,
          context.currentPassage || 'Unknown passage',
          message,
          'observation'
        );
        
        // Get session history for context
        const sessionHistory = await MemoryService.getBibleStudySessionHistory(session.id);
        
        // Generate a personalized response
        const aiResponse = await AIService.generateBibleStudyResponse({
          userMessage: message,
          currentPassage: context.currentPassage || 'Unknown passage',
          questionType: 'observation',
          sessionHistory,
          nextQuestionType: 'interpretation'
        });
        
        // New flow: combined affirmation + interpretation question
        await send(userJid, `${aiResponse.response}\n\n${aiResponse.followUpQuestion}`);

        // Update context
        const updatedContext = {
          ...context,
          lastObservation: message
        };
        
        // Move to interpretation step
        await SessionService.updateSession(session.id, 'AWAITING_INTERPRETATION', updatedContext);
        break;
      }
      
      case 'AWAITING_INTERPRETATION': {
        // Store the user's interpretation response
        await MemoryService.addBibleStudyResponse(
          user.id,
          session.id,
          context.currentPassage || 'Unknown passage',
          message,
          'interpretation'
        );
        
        // Get session history for context
        const sessionHistory = await MemoryService.getBibleStudySessionHistory(session.id);
        
        // Generate a personalized response
        const aiResponse = await AIService.generateBibleStudyResponse({
          userMessage: message,
          currentPassage: context.currentPassage || 'Unknown passage',
          questionType: 'interpretation',
          sessionHistory,
          nextQuestionType: 'application'
        });
        
        // New flow: combined affirmation + application question
        await send(userJid, `${aiResponse.response}\n\n${aiResponse.followUpQuestion}`);

        // Update context
        const updatedContext = {
          ...context,
          lastInterpretation: message
        };
        
        // Move to application step
        await SessionService.updateSession(session.id, 'AWAITING_APPLICATION', updatedContext);
        break;
      }
      
      case 'AWAITING_APPLICATION': {
        // Store the user's application response
        await MemoryService.addBibleStudyResponse(
          user.id,
          session.id,
          context.currentPassage || 'Unknown passage',
          message,
          'application'
        );

        const sessionHistory = await MemoryService.getBibleStudySessionHistory(session.id);

        const aiResponse = await AIService.generateBibleStudyResponse({
          userMessage: message,
          currentPassage: context.currentPassage || 'Unknown passage',
          questionType: 'application',
          sessionHistory,
          nextQuestionType: 'next_passage'
        });

        // Hub & Spoke path is now the default
        const choices = await AIService.generateFollowUpChoices(
          context.currentPassage || 'this passage',
          message
        );

        const combinedMsg = `${aiResponse.response}\n\nWhat feels right for you next?\n${choices.join('\n')}`;
        await send(userJid, combinedMsg);

        const updatedContext = {
          ...context,
          lastApplication: message,
          lastChoices: choices,
          completedPassages: [...(context.completedPassages || []), context.currentPassage]
        };

        await SessionService.updateSession(session.id, 'AWAITING_USER_CHOICE', updatedContext);
        break;
      }

      case 'AWAITING_USER_CHOICE': {
        // (No flag check – new flow always active)
        const choices: string[] = context.lastChoices || [];
        const chosen = parseChoice(message, choices.length);
        if (!chosen) {
          await send(userJid, 'I didn\'t catch that. Could you reply with the number of your choice?');
          return;
        }

        switch (chosen) {
          case 1: {
            await send(userJid, 'Great, let\'s continue.');
            await SessionService.updateSession(session.id, 'SENDING_VERSES', context);
            return this.handleBibleStudyMessage({ ...session, session_step: 'SENDING_VERSES' }, user, '', userJid);
          }
          case 2: {
            const query = context.lastApplication || context.lastInterpretation || message;
            const { RAGService } = await import('./RAGService');
            const verses = await RAGService.search(query);
            if (!verses.length) {
              await send(userJid, 'I couldn\'t find related verses right now. Let\'s continue our study.');
              await SessionService.updateSession(session.id, 'SENDING_VERSES', context);
              return this.handleBibleStudyMessage({ ...session, session_step: 'SENDING_VERSES' }, user, '', userJid);
            }

            const top = verses.slice(0, 3);
            const verseText = top.map(v => `${v.book} ${v.chapter}:${v.verse} – ${v.text}`).join('\n');
            await send(userJid, `Here are some related verses you might find helpful:\n${verseText}`);
            const reflectionQ = await AIService.generateReflectionQuestion(top as any);
            await send(userJid, reflectionQ);
            const newCtx = { ...context, currentPassage: `${top[0].book} ${top[0].chapter}:${top[0].verse}`, currentVerses: top };
            await SessionService.updateSession(session.id, 'AWAITING_OBSERVATION', newCtx);
            break;
          }
          case 3: {
            const passageRef = context.currentPassage || 'this passage';
            const interp = context.lastInterpretation || context.lastObservation || message;
            const q = await AIService.generateApplicationQuestion(passageRef, interp);
            await send(userJid, q);
            await SessionService.updateSessionStep(session.id, 'AWAITING_APPLICATION');
            break;
          }
          default:
            await send(userJid, 'Sorry, I can\'t process that choice.');
        }
        break;
      }

      case 'SESSION_WRAP_UP': {
        // Get all insights from this session
        const sessionHistory = await MemoryService.getBibleStudySessionHistory(session.id);
        const passages = context.completedPassages || [];
        
        // Generate a summary of the session
        const summary = await AIService.generateSessionSummary(
          book,
          passages,
          sessionHistory
        );
        
        await send(userJid, summary);
        await SessionService.endSession(session.id, 'COMPLETED');
        break;
      }
    }
  }
}