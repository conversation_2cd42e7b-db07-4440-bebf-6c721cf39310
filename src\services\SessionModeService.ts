// src/services/SessionModeService.ts
import { logger } from '../config/logger.config';
import { StudyMode, StudySession, User, BaseSessionContext, DeepDiveContext, ChapterOverviewContext, ExploreThemeContext } from '../types';
import { SessionService } from './SessionService';
import { MemoryService } from './MemoryService';
import { StateManager } from './StateManager';
import { messageQueue } from '../queue/messageQueue';
import { ensureMinTypingDelay } from '../utils/messagePacer';
import { shouldUseSessionModeLockIn, getAvailableStudyModes } from '../config/features.config';
import { WelcomeService } from './WelcomeService';

async function send(toJid: string, message: string) {
  await messageQueue.add('send-presence', { toJid, presence: 'composing' });
  
  const typingDelay = Math.min(2500, 800 + message.length * 12);
  const startTime = Date.now();
  
  await ensureMinTypingDelay(startTime, typingDelay);
  await messageQueue.add('send-text', { toJid, text: message });
  await messageQueue.add('send-presence', { toJid, presence: 'paused' });
}

export class SessionModeService {
  /**
   * Check if the new session mode system is enabled
   */
  public static isEnabled(): boolean {
    return shouldUseSessionModeLockIn();
  }

  /**
   * Generate intelligent welcome message for returning users
   */
  public static async generateWelcomeBackMessage(user: User): Promise<string> {
    return WelcomeService.generatePersonalizedWelcome(user);
  }

  /**
   * Generate the study mode selection menu
   */
  public static generateModeSelectionMenu(): string {
    const availableModes = getAvailableStudyModes();
    let menu = `Now, let's choose how you'd like to engage with God's Word today:\n\n`;

    let optionNumber = 1;

    if (availableModes.includes('DEEP_DIVE')) {
      menu += `${optionNumber}. **Deep Dive** - Focus closely on the meaning of a few verses at a time for contemplative, personal reflection.\n\n`;
      optionNumber++;
    }

    if (availableModes.includes('CHAPTER_OVERVIEW')) {
      menu += `${optionNumber}. **Chapter Overview** - Read through a whole chapter to understand the big picture and main narrative flow.\n\n`;
      optionNumber++;
    }

    if (availableModes.includes('EXPLORE_THEME')) {
      menu += `${optionNumber}. **Explore a Theme** - Discover what the Bible says about a specific topic by exploring verses from multiple books.\n\n`;
      optionNumber++;
    }

    menu += `Please reply with the number of your choice.`;

    return menu;
  }

  /**
   * Parse user's mode selection
   */
  public static parseStudyModeChoice(message: string): StudyMode | null {
    const choice = message.trim().toLowerCase();
    const availableModes = getAvailableStudyModes();

    // Handle numeric choices
    const numericChoice = parseInt(choice);
    if (!isNaN(numericChoice) && numericChoice >= 1 && numericChoice <= availableModes.length) {
      return availableModes[numericChoice - 1];
    }

    // Handle text-based choices
    if (choice.includes('deep dive') || choice.includes('deep')) {
      return availableModes.includes('DEEP_DIVE') ? 'DEEP_DIVE' : null;
    } else if (choice.includes('chapter overview') || choice.includes('chapter')) {
      return availableModes.includes('CHAPTER_OVERVIEW') ? 'CHAPTER_OVERVIEW' : null;
    } else if (choice.includes('explore theme') || choice.includes('theme') || choice.includes('explore')) {
      return availableModes.includes('EXPLORE_THEME') ? 'EXPLORE_THEME' : null;
    }

    return null;
  }

  /**
   * Initialize a session with the selected study mode
   */
  public static async initializeStudyModeSession(
    session: StudySession, 
    user: User, 
    studyMode: StudyMode
  ): Promise<BaseSessionContext> {
    const book = user.current_book || 'John';
    const baseContext: BaseSessionContext = {
      book,
      currentStep: 'INITIALIZED',
      previousResponses: [],
      currentPassage: null,
      completedPassages: [],
      studyMode,
      sessionStartTime: new Date().toISOString()
    };

    let specificContext: BaseSessionContext;

    switch (studyMode) {
      case 'DEEP_DIVE':
        specificContext = {
          ...baseContext,
          studyMode: 'DEEP_DIVE',
          currentVerses: [],
          studyBlockCount: 0
        } as DeepDiveContext;
        break;

      case 'CHAPTER_OVERVIEW':
        // Determine next chapter to read
        const nextChapter = (user.last_completed_chapter || 0) + 1;
        specificContext = {
          ...baseContext,
          studyMode: 'CHAPTER_OVERVIEW',
          targetChapter: nextChapter,
          chunksSent: 0,
          totalChunks: 0,
          currentChunkIndex: 0,
          chapterChunks: [],
          userReadyForNext: false
        } as ChapterOverviewContext;
        break;

      case 'EXPLORE_THEME':
        specificContext = {
          ...baseContext,
          studyMode: 'EXPLORE_THEME',
          theme: '',
          curatedVerses: [],
          themeInsights: []
        } as ExploreThemeContext;
        break;

      default:
        throw new Error(`Unknown study mode: ${studyMode}`);
    }

    // Update session with the new context and mode
    await SessionService.updateSession(
      session.id, 
      this.getInitialStepForMode(studyMode), 
      specificContext
    );

    // Also update the study_mode column
    await SessionService.updateStudyMode(session.id, studyMode);

    // Update user's study mode usage and preferences
    await StateManager.updateStudyModeUsage(user.id, studyMode);
    await MemoryService.storeSessionModePreference(user.id, studyMode);

    logger.info({
      sessionId: session.id,
      userId: user.id,
      studyMode
    }, 'Initialized session with study mode');

    return specificContext;
  }

  /**
   * Get the initial step for each study mode
   */
  private static getInitialStepForMode(studyMode: StudyMode): string {
    switch (studyMode) {
      case 'DEEP_DIVE':
        return 'DEEP_DIVE_ACTIVE';
      case 'CHAPTER_OVERVIEW':
        return 'CHAPTER_OVERVIEW_ACTIVE';
      case 'EXPLORE_THEME':
        return 'EXPLORE_THEME_ACTIVE';
      default:
        return 'SENDING_SCENE';
    }
  }



  /**
   * Handle the complete session initiation flow
   */
  public static async handleSessionStart(user: User, userJid: string): Promise<StudySession> {
    // Generate welcome message
    const welcomeMessage = await this.generateWelcomeBackMessage(user);
    await send(userJid, welcomeMessage);

    // Send mode selection menu FIRST
    const modeMenu = this.generateModeSelectionMenu();
    await send(userJid, modeMenu);

    // Generate mode recommendation ONLY for truly first-time users
    const recommendation = await WelcomeService.generateModeRecommendation(user);
    if (recommendation) {
      await send(userJid, recommendation);
    }

    // Create session in mode selection state
    const session = await SessionService.startSession(user.id, {
      type: 'BIBLE_STUDY',
      step: 'AWAITING_MODE_SELECTION',
      context: {
        book: user.current_book || 'John',
        awaitingModeSelection: true,
        sessionStartTime: new Date().toISOString()
      },
      expiresInMinutes: 60
    });

    logger.info({
      sessionId: session.id,
      userId: user.id
    }, 'Started new session mode lock-in session');

    return session;
  }
}
