// src/database/schema.ts
// v3.1: Session-centric, idempotent, and production-ready schema.

export const fullDbSchema = `
  -- Enable extensions
  CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
  CREATE EXTENSION IF NOT EXISTS vector;

  -- Create/Update ENUM types safely
  DO $$ BEGIN CREATE TYPE session_type AS ENUM ('BIBLE_STUDY', 'CHECK_IN', 'REMINDER'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN 
    CREATE TYPE session_status AS ENUM ('ACTIVE', 'PAUSED', 'COMPLETED', 'EXPIRED'); 
  EXCEPTION WHEN duplicate_object THEN
    -- Ensure the 'EXPIRED' value exists if this script runs over an older DB
    ALTER TYPE session_status ADD VALUE IF NOT EXISTS 'EXPIRED';
  END $$;

  DO $$ BEGIN
  CREATE TYPE study_step AS ENUM (
    'SENDING_SCENE',
    'SENDING_VERSES',
    'AWAITING_OBSERVATION',
    'AWAITING_INTERPRETATION',
    'AWAITING_APPLICATION',
    'PRESENTING_CHOICES',
    'AWAITING_USER_CHOICE',
    'STUDY_SESSION_PAUSED',
    'SESSION_WRAP_UP',
    'NUDGE_SENT',
    -- New Session Mode Lock-in steps
    'AWAITING_MODE_SELECTION',
    'DEEP_DIVE_ACTIVE',
    'CHAPTER_OVERVIEW_ACTIVE',
    'CHAPTER_OVERVIEW_WAITING',
    'EXPLORE_THEME_ACTIVE',
    'EXPLORE_THEME_WAITING',
    -- Confession-related steps
    'CONFESSION_PENDING_INPUT',
    'CONFESSION_AWAITING_CONFIRMATION',
    'CONFESSION_AWAITING_REMINDER_TIME',
    'CONFESSION_EDIT_PENDING_INPUT',
    'CONFESSION_EDIT_AWAITING_CONFIRMATION'
  );
EXCEPTION WHEN duplicate_object THEN null;
END $$;

-- Ensure new study_step values exist when type already present
DO $$
BEGIN
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'PRESENTING_CHOICES';
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'AWAITING_USER_CHOICE';
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'STUDY_SESSION_PAUSED';
  -- New Session Mode Lock-in steps
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'AWAITING_MODE_SELECTION';
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'DEEP_DIVE_ACTIVE';
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CHAPTER_OVERVIEW_ACTIVE';
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CHAPTER_OVERVIEW_WAITING';
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'EXPLORE_THEME_ACTIVE';
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'EXPLORE_THEME_WAITING';
  -- Confession-related steps
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CONFESSION_PENDING_INPUT';
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CONFESSION_AWAITING_CONFIRMATION';
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CONFESSION_AWAITING_REMINDER_TIME';
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CONFESSION_EDIT_PENDING_INPUT';
  ALTER TYPE study_step ADD VALUE IF NOT EXISTS 'CONFESSION_EDIT_AWAITING_CONFIRMATION';
EXCEPTION WHEN duplicate_object THEN null;
END $$;

  DO $$ BEGIN
      CREATE TYPE onboarding_step_status AS ENUM ('NEEDS_ONBOARDING', 'AWAITING_AUTH', 'AWAITING_NAME', 'AWAITING_REMINDER_TIME', 'AWAITING_TIMEZONE', 'AWAITING_CONFESSION_CHOICE', 'AWAITING_CONFESSION_INPUT', 'AWAITING_JOURNEY', 'ONBOARDING_COMPLETE');
  EXCEPTION WHEN duplicate_object THEN
      ALTER TYPE onboarding_step_status ADD VALUE IF NOT EXISTS 'AWAITING_TIMEZONE';
      ALTER TYPE onboarding_step_status ADD VALUE IF NOT EXISTS 'AWAITING_JOURNEY';
      ALTER TYPE onboarding_step_status ADD VALUE IF NOT EXISTS 'AWAITING_AUTH';
      ALTER TYPE onboarding_step_status ADD VALUE IF NOT EXISTS 'AWAITING_CONFESSION_CHOICE';
      ALTER TYPE onboarding_step_status ADD VALUE IF NOT EXISTS 'AWAITING_CONFESSION_INPUT';
  END $$;

  -- Helper function to safely add columns
  CREATE OR REPLACE FUNCTION add_column_if_not_exists(p_table_name TEXT, p_column_name TEXT, p_column_definition TEXT)
  RETURNS void AS $$
  BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = p_table_name AND column_name = p_column_name) THEN
      EXECUTE 'ALTER TABLE ' || quote_ident(p_table_name) || ' ADD COLUMN ' || quote_ident(p_column_name) || ' ' || p_column_definition;
    END IF;
  END;
  $$ LANGUAGE plpgsql;

  -- Create and modify Users Table
  CREATE TABLE IF NOT EXISTS users (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), user_hash TEXT NOT NULL UNIQUE, name TEXT, reminder_time_pref TIME WITHOUT TIME ZONE,
      user_timezone TEXT NOT NULL DEFAULT 'UTC', current_book TEXT, current_streak INT DEFAULT 0 NOT NULL,
      is_active BOOLEAN DEFAULT true NOT NULL, created_at TIMESTAMPTZ DEFAULT now() NOT NULL, last_interaction_at TIMESTAMPTZ DEFAULT now() NOT NULL
  );
  SELECT add_column_if_not_exists('users', 'jid', 'TEXT');
  SELECT add_column_if_not_exists('users', 'onboarding_step', 'onboarding_step_status NOT NULL DEFAULT ''NEEDS_ONBOARDING''');
  SELECT add_column_if_not_exists('users', 'last_completed_chapter', 'INT NOT NULL DEFAULT 0');
  SELECT add_column_if_not_exists('users', 'last_completed_verse', 'INT NOT NULL DEFAULT 0');
  SELECT add_column_if_not_exists('users', 'next_reminder_at', 'TIMESTAMPTZ');
  SELECT add_column_if_not_exists('users', 'next_check_in_at', 'TIMESTAMPTZ');
  -- New: verification flag
  SELECT add_column_if_not_exists('users', 'is_verified', 'BOOLEAN NOT NULL DEFAULT false');
  -- Enhanced memory fields for Session Mode Lock-in
  SELECT add_column_if_not_exists('users', 'preferred_study_mode', 'TEXT');
  SELECT add_column_if_not_exists('users', 'total_study_sessions', 'INT NOT NULL DEFAULT 0');
  SELECT add_column_if_not_exists('users', 'last_study_mode_used', 'TEXT');
  SELECT add_column_if_not_exists('users', 'last_theme_explored', 'TEXT');
  DO $$ BEGIN IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'last_reminder_sent_at') THEN ALTER TABLE users DROP COLUMN last_reminder_sent_at; END IF; END $$;

  -- Drop and recreate study_sessions table if it exists but doesn't have session_type column
  DO $$ 
  BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'study_sessions') AND 
       NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'study_sessions' AND column_name = 'session_type') THEN
      DROP TABLE study_sessions CASCADE;
    END IF;
  END $$;

  -- Create / evolve Study Sessions Table (the new core of our logic)
  CREATE TABLE IF NOT EXISTS study_sessions (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      session_type session_type NOT NULL,
      status session_status NOT NULL DEFAULT 'ACTIVE',
      session_step study_step,
      session_context JSONB,
      study_mode TEXT DEFAULT 'DEEP_DIVE',
      prompt_version SMALLINT DEFAULT 1,
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      expires_at TIMESTAMPTZ
  );
  SELECT add_column_if_not_exists('study_sessions', 'study_mode', 'TEXT DEFAULT ''DEEP_DIVE''');
  SELECT add_column_if_not_exists('study_sessions', 'prompt_version', 'SMALLINT DEFAULT 1');

  -- Trigger to touch updated_at on any row update
  CREATE OR REPLACE FUNCTION touch_session_updated_at()
  RETURNS TRIGGER AS $$
  BEGIN
  NEW.updated_at := NOW();
  RETURN NEW;
  END;
  $$ LANGUAGE plpgsql;

  -- Ensure trigger exists exactly once (idempotent)
  DROP TRIGGER IF EXISTS trg_touch_session ON study_sessions;
  CREATE TRIGGER trg_touch_session
    BEFORE UPDATE ON study_sessions
    FOR EACH ROW EXECUTE FUNCTION touch_session_updated_at();

  -- Confessions Table
  CREATE TABLE IF NOT EXISTS confessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    original_text TEXT NOT NULL,
    refined_text TEXT NOT NULL,
    reminder_time TIME WITHOUT TIME ZONE,
    reminder_frequency TEXT DEFAULT 'once',
    next_reminder_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
  );

  -- RAG Content Table (Unchanged)
  CREATE TABLE IF NOT EXISTS rag_content (
    id BIGSERIAL PRIMARY KEY, book TEXT NOT NULL, chapter INT NOT NULL, verse INT NOT NULL, text TEXT NOT NULL,
    translation TEXT NOT NULL, embedding VECTOR(1024), CONSTRAINT uq_verse UNIQUE (book, chapter, verse, translation)
  );

  -- Create Indexes
CREATE INDEX IF NOT EXISTS idx_active_sessions
    ON study_sessions (user_id, status)
    WHERE status = 'ACTIVE';

  -- Sessions that are still ACTIVE but idle long enough to be nudged/expired.
  CREATE INDEX IF NOT EXISTS idx_stale_session_lookup
    ON study_sessions (status, updated_at)
    WHERE status = 'ACTIVE';

  -- Confession indexes
  CREATE INDEX IF NOT EXISTS idx_confessions_user_id
    ON confessions (user_id)
    WHERE is_active = true;

  CREATE INDEX IF NOT EXISTS idx_confessions_reminder_due
    ON confessions (next_reminder_at)
    WHERE is_active = true AND next_reminder_at IS NOT NULL;
`;