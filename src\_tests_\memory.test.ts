import { MemoryService } from '../services/MemoryService';

describe('MemoryService sliding window', () => {
  const userId = 'test-user';

  it('keeps only the last 6 messages', async () => {
    // push 8 messages
    for (let i = 0; i < 8; i++) {
      await MemoryService.addMessageToHistory(userId, {
        role: i % 2 === 0 ? 'user' : 'assistant',
        content: `msg-${i}`,
      });
    }

    const history = await MemoryService.getHistory(userId);
    expect(history.length).toBe(6);
    expect(history[0].content).toBe('msg-2'); // oldest kept
    expect(history[5].content).toBe('msg-7'); // newest kept
  });
});