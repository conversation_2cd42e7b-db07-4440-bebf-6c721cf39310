// src/scripts/validateSessionModePerformance.ts
// Performance validation and optimization for Session Mode Lock-in system

import 'dotenv/config';
import { logger } from '../config/logger.config';
import { pool } from '../database/client';
import { redisConnection } from '../queue/connection';
import { SessionModeService } from '../services/SessionModeService';
import { WelcomeService } from '../services/WelcomeService';
import { MemoryService } from '../services/MemoryService';
import { StateManager } from '../services/StateManager';
import { SessionService } from '../services/SessionService';
import { shouldUseSessionModeLockIn } from '../config/features.config';

interface PerformanceMetrics {
  operation: string;
  duration: number;
  success: boolean;
  error?: string;
}

class PerformanceValidator {
  private metrics: PerformanceMetrics[] = [];

  async measureOperation<T>(
    operation: string,
    fn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      
      this.metrics.push({
        operation,
        duration,
        success: true
      });
      
      logger.info({ operation, duration }, 'Operation completed');
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.metrics.push({
        operation,
        duration,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      logger.error({ operation, duration, error }, 'Operation failed');
      throw error;
    }
  }

  getMetrics(): PerformanceMetrics[] {
    return this.metrics;
  }

  getAverageTime(operation: string): number {
    const operationMetrics = this.metrics.filter(m => m.operation === operation && m.success);
    if (operationMetrics.length === 0) return 0;
    
    const totalTime = operationMetrics.reduce((sum, m) => sum + m.duration, 0);
    return totalTime / operationMetrics.length;
  }

  getSuccessRate(operation: string): number {
    const operationMetrics = this.metrics.filter(m => m.operation === operation);
    if (operationMetrics.length === 0) return 0;
    
    const successCount = operationMetrics.filter(m => m.success).length;
    return (successCount / operationMetrics.length) * 100;
  }
}

async function validateSessionModePerformance() {
  const validator = new PerformanceValidator();
  
  logger.info('Starting Session Mode Lock-in performance validation...');

  try {
    // 1. Database Performance Tests
    logger.info('Testing database performance...');
    
    await validator.measureOperation('database-connection', async () => {
      const client = await pool.connect();
      client.release();
    });

    await validator.measureOperation('user-query', async () => {
      const { rows } = await pool.query('SELECT COUNT(*) FROM users');
      return rows[0].count;
    });

    await validator.measureOperation('session-query', async () => {
      const { rows } = await pool.query('SELECT COUNT(*) FROM study_sessions WHERE status = $1', ['ACTIVE']);
      return rows[0].count;
    });

    // Test new indexes
    await validator.measureOperation('study-mode-index-query', async () => {
      const { rows } = await pool.query(`
        SELECT COUNT(*) FROM users 
        WHERE preferred_study_mode IS NOT NULL
      `);
      return rows[0].count;
    });

    // 2. Redis Performance Tests
    logger.info('Testing Redis performance...');
    
    await validator.measureOperation('redis-set', async () => {
      await redisConnection.set('test-key', 'test-value', 'EX', 60);
    });

    await validator.measureOperation('redis-get', async () => {
      return await redisConnection.get('test-key');
    });

    await validator.measureOperation('redis-cleanup', async () => {
      await redisConnection.del('test-key');
    });

    // 3. Memory Service Performance
    logger.info('Testing memory service performance...');
    
    const testUserId = 'perf-test-user';
    
    await validator.measureOperation('memory-store-insight', async () => {
      await MemoryService.addBibleStudyResponse(
        testUserId,
        'test-session',
        'John 3:16',
        'This verse shows God\'s love',
        'observation'
      );
    });

    await validator.measureOperation('memory-get-insights', async () => {
      return await MemoryService.getUserBibleInsights(testUserId, 'John');
    });

    await validator.measureOperation('memory-store-theme', async () => {
      await MemoryService.addThemeExploration(
        testUserId,
        'love',
        [{ book: 'John', chapter: 3, verse: 16, text: 'For God so loved...' }],
        'God\'s love is sacrificial'
      );
    });

    await validator.measureOperation('memory-get-themes', async () => {
      return await MemoryService.getThemeExplorations(testUserId);
    });

    // 4. Session Mode Service Performance
    logger.info('Testing session mode service performance...');
    
    await validator.measureOperation('mode-menu-generation', async () => {
      return SessionModeService.generateModeSelectionMenu();
    });

    await validator.measureOperation('mode-choice-parsing', async () => {
      return SessionModeService.parseStudyModeChoice('1');
    });

    // 5. Welcome Service Performance
    logger.info('Testing welcome service performance...');
    
    const testUser = {
      id: testUserId,
      user_hash: 'test-hash-performance',
      jid: '<EMAIL>',
      name: 'Test User',
      reminder_time_pref: null,
      user_timezone: 'UTC',
      current_book: 'John',
      last_completed_chapter: 1,
      last_completed_verse: 10,
      current_streak: 0,
      onboarding_step: 'ONBOARDING_COMPLETE' as const,
      is_active: true,
      is_verified: true,
      next_reminder_at: null,
      next_check_in_at: null,
      preferred_study_mode: null,
      total_study_sessions: 3,
      last_study_mode_used: 'DEEP_DIVE',
      last_theme_explored: 'love',
      created_at: new Date(),
      last_interaction_at: new Date()
    };

    await validator.measureOperation('welcome-new-user-check', async () => {
      return await WelcomeService.isNewUserForBook(testUser);
    });

    await validator.measureOperation('welcome-message-generation', async () => {
      return await WelcomeService.generatePersonalizedWelcome(testUser);
    });

    await validator.measureOperation('welcome-recommendation', async () => {
      return await WelcomeService.generateModeRecommendation(testUser);
    });

    // 6. State Manager Performance
    logger.info('Testing state manager performance...');
    
    await validator.measureOperation('state-preferences-query', async () => {
      return await StateManager.getUserStudyPreferences(testUserId);
    });

    // 7. Concurrent Operations Test
    logger.info('Testing concurrent operations...');
    
    await validator.measureOperation('concurrent-mode-parsing', async () => {
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(SessionModeService.parseStudyModeChoice(String((i % 3) + 1)));
      }
      return await Promise.all(promises);
    });

    await validator.measureOperation('concurrent-memory-operations', async () => {
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(
          MemoryService.addBibleStudyResponse(
            `${testUserId}-${i}`,
            `test-session-${i}`,
            'John 1:1',
            `Test insight ${i}`,
            'observation'
          )
        );
      }
      return await Promise.all(promises);
    });

    // 8. Memory Usage Test
    logger.info('Testing memory usage...');
    
    const initialMemory = process.memoryUsage();
    
    // Perform memory-intensive operations
    for (let i = 0; i < 100; i++) {
      SessionModeService.generateModeSelectionMenu();
      SessionModeService.parseStudyModeChoice('1');
    }
    
    const finalMemory = process.memoryUsage();
    const memoryDelta = {
      heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
      heapTotal: finalMemory.heapTotal - initialMemory.heapTotal,
      external: finalMemory.external - initialMemory.external
    };
    
    logger.info({ initialMemory, finalMemory, memoryDelta }, 'Memory usage analysis');

    // 9. Error Handling Performance
    logger.info('Testing error handling performance...');
    
    await validator.measureOperation('invalid-mode-handling', async () => {
      return SessionModeService.parseStudyModeChoice('invalid');
    });

    await validator.measureOperation('non-existent-user-memory', async () => {
      return await MemoryService.getUserBibleInsights('non-existent-user', 'John');
    });

    // 10. Cleanup Test Data
    logger.info('Cleaning up test data...');
    
    await validator.measureOperation('cleanup-redis', async () => {
      const keys = await redisConnection.keys(`*${testUserId}*`);
      if (keys.length > 0) {
        await redisConnection.del(...keys);
      }
    });

    // Generate Performance Report
    const metrics = validator.getMetrics();
    const report = {
      totalOperations: metrics.length,
      successfulOperations: metrics.filter(m => m.success).length,
      failedOperations: metrics.filter(m => !m.success).length,
      averageTimes: {
        'database-connection': validator.getAverageTime('database-connection'),
        'memory-operations': (
          validator.getAverageTime('memory-store-insight') +
          validator.getAverageTime('memory-get-insights')
        ) / 2,
        'session-mode-operations': (
          validator.getAverageTime('mode-menu-generation') +
          validator.getAverageTime('mode-choice-parsing')
        ) / 2,
        'welcome-operations': (
          validator.getAverageTime('welcome-new-user-check') +
          validator.getAverageTime('welcome-message-generation')
        ) / 2
      },
      successRates: {
        'database-operations': validator.getSuccessRate('database-connection'),
        'redis-operations': validator.getSuccessRate('redis-set'),
        'memory-operations': validator.getSuccessRate('memory-store-insight'),
        'session-mode-operations': validator.getSuccessRate('mode-menu-generation')
      }
    };

    logger.info({ report }, '📊 Performance validation report');

    // Performance Recommendations
    const recommendations = [];
    
    if (report.averageTimes['database-connection'] > 100) {
      recommendations.push('Consider database connection pooling optimization');
    }
    
    if (report.averageTimes['memory-operations'] > 50) {
      recommendations.push('Consider Redis connection optimization');
    }
    
    if (report.successRates['database-operations'] < 95) {
      recommendations.push('Investigate database connection reliability');
    }
    
    if (memoryDelta.heapUsed > 10 * 1024 * 1024) { // 10MB
      recommendations.push('Consider memory usage optimization');
    }

    if (recommendations.length > 0) {
      logger.warn({ recommendations }, '⚠️ Performance recommendations');
    } else {
      logger.info('✅ All performance metrics are within acceptable ranges');
    }

    logger.info('✅ Session Mode Lock-in performance validation completed successfully!');

  } catch (error) {
    logger.error({ error }, '❌ Session Mode Lock-in performance validation failed');
    throw error;
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  validateSessionModePerformance()
    .then(() => {
      logger.info('Performance validation script completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error({ error }, 'Performance validation script failed');
      process.exit(1);
    });
}

export { validateSessionModePerformance };
