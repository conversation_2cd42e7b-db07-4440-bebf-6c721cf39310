// src/services/ChapterOverviewModeHandler.ts
// Chapter Overview mode with dynamic chunking implementation

import { logger } from '../config/logger.config';
import { StudySession, User, ChapterOverviewContext } from '../types';
import { SessionService } from './SessionService';
import { StateManager } from './StateManager';
import { AIService } from './AIService';
import { MemoryService } from './MemoryService';
import { messageQueue } from '../queue/messageQueue';
import { ensureMinTypingDelay } from '../utils/messagePacer';
import { pool } from '../database/client';

async function send(toJid: string, message: string) {
  await messageQueue.add('send-presence', { toJid, presence: 'composing' });
  
  const typingDelay = Math.min(2500, 800 + message.length * 12);
  const startTime = Date.now();
  
  await ensureMinTypingDelay(startTime, typingDelay);
  await messageQueue.add('send-text', { toJid, text: message });
  await messageQueue.add('send-presence', { toJid, presence: 'paused' });
}

interface ChapterChunk {
  startVerse: number;
  endVerse: number;
  text: string;
  characterCount: number;
}

export class ChapterOverviewModeHandler {
  private static readonly MAX_CHUNK_CHARACTERS = 900; // Mobile-friendly limit
  private static readonly MIN_CHUNK_CHARACTERS = 400; // Minimum for meaningful chunks
  private static readonly CHUNK_DELIVERY_DELAY = 3000; // 3 seconds between chunks

  /**
   * Handle Chapter Overview mode interactions
   */
  public static async handle(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ChapterOverviewContext;
    const step = session.session_step;

    logger.info({
      sessionId: session.id,
      userId: user.id,
      step,
      targetChapter: context?.targetChapter,
      chunksSent: context?.chunksSent || 0
    }, 'Handling Chapter Overview mode interaction');

    switch (step) {
      case 'CHAPTER_OVERVIEW_ACTIVE':
        if (!context.chapterChunks || context.chapterChunks.length === 0) {
          // Initialize chapter chunking
          await this.initializeChapterReading(session, user, userJid);
        } else if (context.currentChunkIndex >= context.chapterChunks.length) {
          // All chunks have been sent, should move to discussion
          await this.startChapterDiscussion(session, user, userJid);
        } else {
          // Continue sending chunks or handle user input
          await this.handleChapterInteraction(session, user, message, userJid);
        }
        break;

      case 'CHAPTER_OVERVIEW_WAITING':
        // User is ready for next chunk or discussion
        await this.handleUserReadySignal(session, user, message, userJid);
        break;

      default:
        logger.warn({ sessionId: session.id, step }, 'Unknown Chapter Overview step');
        await send(userJid, "Let's continue with our chapter reading.");
        break;
    }
  }

  /**
   * Initialize chapter reading with dynamic chunking
   */
  private static async initializeChapterReading(
    session: StudySession,
    user: User,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ChapterOverviewContext;
    const book = context.book;
    const targetChapter = context.targetChapter;

    try {
      // Get all verses for the target chapter
      const chapterVerses = await this.getChapterVerses(book, targetChapter);

      if (chapterVerses.length === 0) {
        await send(userJid, `I couldn't find chapter ${targetChapter} in ${book}.\n\nLet me check what's available.`);
        await SessionService.endSession(session.id, 'COMPLETED');
        return;
      }

      // Create dynamic chunks
      const chunks = this.createDynamicChunks(chapterVerses);

      // Send introduction
      const intro = `Let's read through ${book} chapter ${targetChapter} together. I'll send it in ${chunks.length} readable sections. Take your time with each part - just say "ready" or "next" when you'd like to continue.`;
      await send(userJid, intro);

      // Update context with chunks
      const updatedContext: ChapterOverviewContext = {
        ...context,
        chapterChunks: chunks,
        totalChunks: chunks.length,
        currentChunkIndex: 0,
        chunksSent: 0,
        userReadyForNext: false
      };

      await SessionService.updateSessionContext(session.id, updatedContext);

      // Get fresh session with updated context before starting chunk delivery
      const updatedSession = await SessionService.findActiveSession(user.id);
      if (!updatedSession) {
        logger.error({ sessionId: session.id }, 'Session not found after context update');
        return;
      }

      // Start sending chunks automatically with delays
      await this.startChunkDelivery(updatedSession, user, userJid);

    } catch (error) {
      logger.error({ error, sessionId: session.id, book, targetChapter }, 'Failed to initialize chapter reading');
      await send(userJid, "I'm having trouble loading this chapter. Let me try again.");
    }
  }

  /**
   * Get all verses for a specific chapter
   */
  private static async getChapterVerses(book: string, chapter: number): Promise<Array<{verse: number, text: string}>> {
    const query = `
      SELECT verse, text 
      FROM rag_content 
      WHERE book = $1 AND chapter = $2 
      ORDER BY verse ASC
    `;
    
    const { rows } = await pool.query(query, [book, chapter]);
    return rows;
  }

  /**
   * Create dynamic chunks based on character count and natural breaks
   */
  private static createDynamicChunks(verses: Array<{verse: number, text: string}>): ChapterChunk[] {
    const chunks: ChapterChunk[] = [];
    let currentChunk = '';
    let startVerse = verses[0]?.verse || 1;
    let currentVerse = startVerse;

    for (const verse of verses) {
      const verseText = `**${verse.verse}** ${verse.text}\n\n`;
      const potentialChunk = currentChunk + verseText;

      // Check if adding this verse would exceed our limit
      if (potentialChunk.length > this.MAX_CHUNK_CHARACTERS && currentChunk.length > this.MIN_CHUNK_CHARACTERS) {
        // Save current chunk and start new one
        chunks.push({
          startVerse,
          endVerse: currentVerse,
          text: currentChunk.trim(),
          characterCount: currentChunk.length
        });

        // Start new chunk
        currentChunk = verseText;
        startVerse = verse.verse;
        currentVerse = verse.verse;
      } else {
        // Add verse to current chunk
        currentChunk = potentialChunk;
        currentVerse = verse.verse;
      }
    }

    // Add final chunk if there's content
    if (currentChunk.trim()) {
      chunks.push({
        startVerse,
        endVerse: currentVerse,
        text: currentChunk.trim(),
        characterCount: currentChunk.length
      });
    }

    return chunks;
  }

  /**
   * Start automatic chunk delivery with delays
   */
  private static async startChunkDelivery(
    session: StudySession,
    user: User,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ChapterOverviewContext;
    
    // Send first chunk immediately
    await this.sendNextChunk(session, user, userJid);
    
    // Schedule remaining chunks with delays
    this.scheduleRemainingChunks(session, user, userJid);
  }

  /**
   * Send the next chunk in sequence
   */
  private static async sendNextChunk(
    session: StudySession,
    user: User,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ChapterOverviewContext;
    const currentIndex = context.currentChunkIndex || 0;

    logger.info({
      sessionId: session.id,
      currentIndex,
      totalChunks: context.chapterChunks?.length || 0,
      chunksSent: context.chunksSent || 0
    }, 'Sending next chunk');

    if (!context.chapterChunks || context.chapterChunks.length === 0) {
      logger.error({ sessionId: session.id }, 'No chapter chunks available');
      await send(userJid, "I'm having trouble loading the chapter content. Let me try again.");
      return;
    }

    if (currentIndex >= context.chapterChunks.length) {
      // All chunks sent, move to discussion
      await this.startChapterDiscussion(session, user, userJid);
      return;
    }

    const chunk = context.chapterChunks[currentIndex];
    const chunkHeader = `**${context.book} ${context.targetChapter}:${chunk.startVerse}${chunk.startVerse !== chunk.endVerse ? `-${chunk.endVerse}` : ''}**\n\n`;
    const chunkMessage = chunkHeader + chunk.text;

    await send(userJid, chunkMessage);

    // Update context
    const updatedContext: ChapterOverviewContext = {
      ...context,
      currentChunkIndex: currentIndex + 1,
      chunksSent: (context.chunksSent || 0) + 1
    };

    await SessionService.updateSessionContext(session.id, updatedContext);

    // Store progress
    await MemoryService.storeChapterProgress(
      user.id,
      session.id,
      context.book,
      context.targetChapter,
      updatedContext.chunksSent,
      context.totalChunks
    );

    logger.info({
      sessionId: session.id,
      chunksSent: updatedContext.chunksSent,
      totalChunks: context.totalChunks
    }, 'Sent chapter chunk');
  }

  /**
   * Schedule remaining chunks with delays
   */
  private static scheduleRemainingChunks(
    session: StudySession,
    user: User,
    userJid: string
  ): void {
    const context = session.session_context as ChapterOverviewContext;
    const remainingChunks = context.totalChunks - (context.chunksSent || 0);

    for (let i = 1; i < remainingChunks; i++) {
      setTimeout(async () => {
        try {
          // Get fresh session context
          const currentSession = await SessionService.findActiveSession(user.id);
          if (currentSession && currentSession.id === session.id) {
            await this.sendNextChunk(currentSession, user, userJid);
          }
        } catch (error) {
          logger.error({ error, sessionId: session.id }, 'Error in scheduled chunk delivery');
        }
      }, this.CHUNK_DELIVERY_DELAY * i);
    }
  }

  /**
   * Handle user interaction during chapter reading
   */
  private static async handleChapterInteraction(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ChapterOverviewContext;
    const userMessage = message.toLowerCase().trim();

    // Check if this is a next chapter continuation request
    if ((userMessage.includes('continue') || userMessage.includes('next') || userMessage.includes('ready')) &&
        context.chapterChunks.length > 0 && context.currentChunkIndex >= context.chapterChunks.length) {
      // User wants to continue to next chapter - reinitialize for new chapter
      await send(userJid, `Great! Let's dive into ${context.book} chapter ${context.targetChapter}...`);
      await this.initializeChapterReading(session, user, userJid);
      return;
    }

    // Check if user wants to pause or has questions
    if (userMessage.includes('pause') || userMessage.includes('stop') || userMessage.includes('wait')) {
      await send(userJid, "Of course! Take your time. Just say 'continue' when you're ready to keep reading.");
      await SessionService.updateSessionStep(session.id, 'CHAPTER_OVERVIEW_WAITING');
    } else if (userMessage.includes('question') || userMessage === '?') {
      await send(userJid, "What's your question? I'm here to help!");
    } else if (userMessage.includes('continue') || userMessage.includes('next') || userMessage.includes('ready')) {
      // Continue with current chapter reading
      await this.sendNextChunk(session, user, userJid);
    } else {
      // Default response for unclear input
      await send(userJid, "Say 'continue' to keep reading, 'pause' to take a break, or ask me a question!");
    }
  }

  /**
   * Handle user ready signal or discussion response
   */
  private static async handleUserReadySignal(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ChapterOverviewContext;
    const userMessage = message.toLowerCase().trim();

    // Check if we're in discussion phase
    if (context.discussionActive) {
      await this.handleDiscussionResponse(session, user, message, userJid);
      return;
    }

    // Handle reading continuation or next chapter progression
    if (userMessage.includes('ready') || userMessage.includes('continue') || userMessage.includes('next')) {
      // Check if we're waiting for next chapter continuation
      if (context.targetChapter > 1 && context.chapterChunks.length === 0) {
        // User wants to continue to next chapter
        await send(userJid, `Great! Let's dive into ${context.book} chapter ${context.targetChapter}...`);
        await SessionService.updateSessionStep(session.id, 'CHAPTER_OVERVIEW_ACTIVE');
        await this.initializeChapterReading(session, user, userJid);
      } else {
        // Normal reading continuation within current chapter
        await send(userJid, "Great! Continuing with our reading...");
        await SessionService.updateSessionStep(session.id, 'CHAPTER_OVERVIEW_ACTIVE');
        await this.sendNextChunk(session, user, userJid);
      }
    } else {
      await send(userJid, "Just say 'ready' or 'continue' when you'd like to keep reading.");
    }
  }

  /**
   * Handle discussion responses and manage question flow
   */
  private static async handleDiscussionResponse(
    session: StudySession,
    user: User,
    message: string,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ChapterOverviewContext;
    const questionsAsked = context.questionsAsked || 0;
    const maxQuestions = context.maxQuestions || 3;

    // Store user's response
    await MemoryService.addBibleStudyResponse(
      user.id,
      session.id,
      `${context.book} ${context.targetChapter}`,
      message,
      'interpretation'
    );

    // Check if we've reached the question limit
    if (questionsAsked >= maxQuestions - 1) {
      // Final question reached, move to next chapter
      await this.concludeChapterAndMoveNext(session, user, userJid);
    } else {
      // Ask another question
      await this.askNextDiscussionQuestion(session, user, userJid);
    }
  }

  /**
   * Ask the next discussion question
   */
  private static async askNextDiscussionQuestion(
    session: StudySession,
    user: User,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ChapterOverviewContext;

    // Generate follow-up question
    const followUpQuestion = await AIService.generateChapterOverviewDiscussion(
      context.book,
      context.targetChapter
    );

    await send(userJid, followUpQuestion);

    // Update question count
    const updatedContext: ChapterOverviewContext = {
      ...context,
      questionsAsked: (context.questionsAsked || 0) + 1
    };

    await SessionService.updateSessionContext(session.id, updatedContext);
  }

  /**
   * Conclude current chapter and move to next
   */
  private static async concludeChapterAndMoveNext(
    session: StudySession,
    user: User,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ChapterOverviewContext;

    await send(userJid, `Thank you for that thoughtful reflection! You've completed ${context.book} chapter ${context.targetChapter}. 🎉`);

    // Update user progress
    await StateManager.updateUserJourney(user.id, context.book);

    // Prepare for next chapter continuation
    const nextChapter = context.targetChapter + 1;
    await send(userJid, `Ready to continue with ${context.book} chapter ${nextChapter}? Type "continue" to move to the next chapter!`);

    // Update session context for next chapter instead of ending session
    const nextChapterContext: ChapterOverviewContext = {
      ...context,
      targetChapter: nextChapter,
      chunksSent: 0,
      totalChunks: 0,
      currentChunkIndex: 0,
      chapterChunks: [],
      userReadyForNext: false,
      discussionActive: false,
      questionsAsked: 0,
      awaitingResponse: true
    };

    await SessionService.updateSessionContext(session.id, nextChapterContext);
    await SessionService.updateSessionStep(session.id, 'CHAPTER_OVERVIEW_WAITING');

    logger.info({
      sessionId: session.id,
      userId: user.id,
      book: context.book,
      completedChapter: context.targetChapter - 1, // Previous chapter that was completed
      nextChapter: context.targetChapter,
      questionsAsked: context.questionsAsked
    }, 'Completed chapter discussion, prepared for next chapter continuation');
  }

  /**
   * Start chapter discussion after all chunks are sent
   */
  private static async startChapterDiscussion(
    session: StudySession,
    user: User,
    userJid: string
  ): Promise<void> {
    const context = session.session_context as ChapterOverviewContext;

    await send(userJid, `📖 We've finished reading ${context.book} chapter ${context.targetChapter}! What a journey that was.`);

    // Initialize discussion context
    const updatedContext: ChapterOverviewContext = {
      ...context,
      discussionActive: true,
      questionsAsked: 0,
      maxQuestions: 3,
      awaitingResponse: true
    };

    await SessionService.updateSessionContext(session.id, updatedContext);
    await SessionService.updateSessionStep(session.id, 'CHAPTER_OVERVIEW_WAITING');

    // Generate and send first discussion question
    const discussionPrompt = await AIService.generateChapterOverviewDiscussion(
      context.book,
      context.targetChapter
    );

    await send(userJid, discussionPrompt);

    logger.info({
      sessionId: session.id,
      userId: user.id,
      book: context.book,
      chapter: context.targetChapter
    }, 'Started Chapter Overview discussion');
  }
}
