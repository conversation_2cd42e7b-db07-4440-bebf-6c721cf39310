// src/types/index.ts
import { z } from 'zod';

export const OnboardingStepSchema = z.enum([
  'NEEDS_ONBOARDING',
  'AWAITING_AUTH', // NEW – awaiting keyword verification
  'AWAITING_NAME',
  'AWAITING_REMINDER_TIME',
  'AWAITING_TIMEZONE',
  'AWAITING_CONFESSION_CHOICE',
  'AWAITING_CONFESSION_INPUT', // NEW - awaiting confession text during onboarding
  'AWAITING_JOURNEY',
  'ONBOARDING_COMPLETE'
]);
export const SessionTypeSchema = z.enum(['BIBLE_STUDY', 'CHECK_IN', 'REMINDER']);
export const SessionStatusSchema = z.enum(['ACTIVE', 'PAUSED', 'COMPLETED', 'EXPIRED']);
export const StudyStepSchema = z.enum(['SENDING_SCENE', 'SENDING_VERSES', 'AWAITING_OBSERVATION', 'AWAITING_INTERPRETATION', 'AWAITING_APPLICATION', 'PRESENTING_CHOICES', 'AWAITING_USER_CHOICE', 'STUDY_SESSION_PAUSED', 'SESSION_WRAP_UP',
  'NUDGE_SENT',
  // New Session Mode Lock-in steps
  'AWAITING_MODE_SELECTION',
  'DEEP_DIVE_ACTIVE',
  'CHAPTER_OVERVIEW_ACTIVE',
  'CHAPTER_OVERVIEW_WAITING',
  'EXPLORE_THEME_ACTIVE',
  'EXPLORE_THEME_WAITING',
  // Confession-related steps
  'CONFESSION_PENDING_INPUT',
  'CONFESSION_AWAITING_CONFIRMATION',
  'CONFESSION_AWAITING_REMINDER_TIME',
  'CONFESSION_EDIT_PENDING_INPUT',
  'CONFESSION_EDIT_AWAITING_CONFIRMATION'
]);

// New Study Mode definitions for Session Mode Lock-in
export const StudyModeSchema = z.enum([
  'DEEP_DIVE',
  'CHAPTER_OVERVIEW',
  'EXPLORE_THEME'
]);

export type StudyMode = z.infer<typeof StudyModeSchema>;

export interface RAGVerse {
  book: string;
  chapter: number;
  verse: number;
  text: string;
  distance: number; // The similarity score from pgvector
}

export const UserSchema = z.object({
  id: z.string().uuid(),
  user_hash: z.string(),
  name: z.string().nullable(), // ADDED
  reminder_time_pref: z.string().nullable(),
  user_timezone: z.string().nullable(),
  current_book: z.string().nullable(),
  last_completed_chapter: z.number().nullable(), // ADDED
  last_completed_verse: z.number().nullable(),   // ADDED
  current_streak: z.number().int(),
  jid: z.string(),
  onboarding_step: OnboardingStepSchema, // ADDED
  is_active: z.boolean(),
  // New auth flag – one-time verification
  is_verified: z.boolean(),
  // These new fields will trigger our schedulers
  next_reminder_at: z.date().nullable(),
  next_check_in_at: z.date().nullable(),
  // Enhanced memory fields for Session Mode Lock-in
  preferred_study_mode: z.string().nullable(),
  total_study_sessions: z.number().int().default(0),
  last_study_mode_used: z.string().nullable(),
  last_theme_explored: z.string().nullable(),
  created_at: z.date(),
  last_interaction_at: z.date(),
});

export const StudySessionSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  session_type: SessionTypeSchema,
  status: SessionStatusSchema,
  session_context: z.any().nullable(), // For storing JSONB data like { book: 'John' }
  session_step: StudyStepSchema.optional().nullable(), // Updated to use proper schema
  study_mode: StudyModeSchema.optional().nullable(), // Updated to use proper schema
  prompt_version: z.number().optional().nullable(), // NEW
  created_at: z.date(),
  updated_at: z.date(),
  expires_at: z.date().nullable(),
});

export const ConfessionSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  original_text: z.string(),
  refined_text: z.string(),
  reminder_time: z.string().nullable(),
  reminder_frequency: z.string().default('once'),
  next_reminder_at: z.date().nullable(),
  is_active: z.boolean().default(true),
  created_at: z.date(),
  updated_at: z.date(),
});

export type User = z.infer<typeof UserSchema>;
export type StudySession = z.infer<typeof StudySessionSchema>;
export type Confession = z.infer<typeof ConfessionSchema>;

export interface ChatMessage {
  role: 'user' | 'assistant'| 'system';
  content: string;
}

// Session Context interfaces for different study modes
export interface BaseSessionContext {
  book: string;
  currentStep: string;
  previousResponses: any[];
  currentPassage: string | null;
  completedPassages?: string[];
  studyMode: StudyMode;
  sessionStartTime: string;
}

export interface DeepDiveContext extends BaseSessionContext {
  studyMode: 'DEEP_DIVE';
  currentVerses: Array<{chapter: number, verse: number, text: string}>;
  lastObservation?: string;
  lastInterpretation?: string;
  lastApplication?: string;
  studyBlockCount: number;
}

export interface ChapterOverviewContext extends BaseSessionContext {
  studyMode: 'CHAPTER_OVERVIEW';
  targetChapter: number;
  chunksSent: number;
  totalChunks: number;
  currentChunkIndex: number;
  chapterChunks: Array<{
    startVerse: number;
    endVerse: number;
    text: string;
    characterCount: number;
  }>;
  userReadyForNext: boolean;
  // Discussion phase fields
  discussionActive?: boolean;
  questionsAsked?: number;
  maxQuestions?: number;
  awaitingResponse?: boolean;
}

export interface ExploreThemeContext extends BaseSessionContext {
  studyMode: 'EXPLORE_THEME';
  theme: string;
  curatedVerses: Array<{
    book: string;
    chapter: number;
    verse: number;
    text: string;
    relevanceScore: number;
  }>;
  themeInsights: string[];
}

export interface ConfessionContext extends BaseSessionContext {
  originalText?: string;
  refinedText?: string;
  reminderTime?: string;
  reminderFrequency?: string;
  isEditing?: boolean;
  editingConfessionId?: string;
  isOnboarding?: boolean;
}
